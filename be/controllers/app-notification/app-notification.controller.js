const AppNotification = require('../../models/app-notification/app-notification')
const User = require('../../models/user/user')
const ErrorResponse = require('../../utils/errorResponse')
const asyncHandler = require('../../middleware/async')
const { io } = require('../../utils/socket')

exports.getGlobalNotifications = asyncHandler(async (req, res, next) => {
  console.log({ req })

  const notifications = await AppNotification.find({ user: req.user })

  io.to(req.user).emit('allAppNotifications', {
    status: 'success',
    data: notifications,
  })
})

exports.sendGlobalNotification = asyncHandler(async (req, res, next) => {
  try {
    const users = await User.find({}, '_id')
    const userIds = users.map(user => user._id)

    const notification = await AppNotification.create({
      title: req.title,
      description: req.description,
      sendToBrowser: req.sendToBrowser,
      sendToApp: req.sendToApp,
      sendToEmail: req.sendToEmail,
      sendWhen: 'online',
      isGlobal: true,
      isRead: false,
      user: userIds,
    })

    const notifications = await AppNotification.find()

    io.to(req.user).emit('sendGlobalAppNotifications', {
      status: 'success',
      message: 'Notification Sent Successfully',
      data: {
        notification,
        notifications,
      },
    })
  } catch (error) {
    console.error(error)
    io.to(req.user).emit('sendGlobalAppNotifications', {
      status: 'error',
      message: error,
    })
  }
})

exports.sendSingleUserNotification = asyncHandler(async (req, res, next) => {
  const notification = await AppNotification.create(req)

  io.to(req.user).emit('sendSingleUserAppNotifications', {
    status: 'success',
    data: notification,
  })
})

exports.markAppAsRead = asyncHandler(async (req, res, next) => {
  const { id } = req
  const userId = req.user

  try {
    const updated = await AppNotification.findOneAndUpdate(
      { _id: id, user: userId },
      { isRead: true },
      { new: true },
    )

    if (!updated) {
      io.to(req.user).emit('markAsReadSystem', {
        status: 'error',
        message: 'Notification Not Found',
      })
    }

    const notifications = await AppNotification.find()

    io.to(req.user).emit('markAsReadSystem', {
      status: 'success',
      data: notifications,
    })
  } catch (err) {
    io.to(req.user).emit('markAsReadSystem', {
      status: 'error',
      message: 'Error marking notification as read',
    })
  }
})

exports.markAppAsUnread = asyncHandler(async (req, res, next) => {
  const { id } = req
  const userId = req.user

  try {
    const updated = await AppNotification.findOneAndUpdate(
      { _id: id, user: userId },
      { isRead: false },
      { new: true },
    )

    if (!updated) {
      io.to(req.user).emit('markAsUnreadSystem', {
        status: 'error',
        message: 'Notification Not Found',
      })
    }

    const notifications = await AppNotification.find()

    io.to(req.user).emit('markAsUnreadSystem', {
      status: 'success',
      data: notifications,
    })
  } catch (err) {
    io.to(req.user).emit('markAsUnreadSystem', {
      status: 'error',
      message: 'Error marking notification as unread',
    })
  }
})