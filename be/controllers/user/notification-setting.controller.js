const NotificationSetting = require('../../models/user/notification-setting')
const User = require('../../models/user/user')
const ErrorResponse = require('../../utils/errorResponse')
const asyncHandler = require('../../middleware/async')
const { io } = require('../../utils/socket')

exports.getNotificationSettings = asyncHandler(async (req, res, next) => {
  console.log('notificationSetting', req)

  const notificationSetting = await NotificationSetting.find({ user: req.user })

  io.to(req.user).emit('allNotificationSettings', {
    status: 'success',
    data: notificationSetting,
  })
})

exports.createNotificationSetting = asyncHandler(async (req, res, next) => {
  console.log('notificationSetting', req)

  const notificationSetting = await NotificationSetting.create(req)

  console.log({ notificationSetting })

  io.to(req.user).emit('createNotificationSetting', {
    status: 'success',
    message: 'Notification Setting Created Successfully',
    data: notificationSetting,
  })
})

exports.updateNotificationSetting = asyncHandler(async (req, res, next) => {
  console.log('notificationSetting', req)

  const notificationSetting = await NotificationSetting.findByIdAndUpdate(req.id, {
    recentDevices: req.recentDevices,
    selectedNotification: req.selectedNotification,
  }, {
    new: true,
    runValidators: true,
  })

  console.log({ notificationSetting })

  io.to(req.user).emit('updateNotificationSetting', {
    status: 'success',
    message: 'Notification Setting Updated Successfully',
    data: notificationSetting,
  })
})