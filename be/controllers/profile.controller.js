const Profile = require('../models/user/profile')
const User = require('../models/user/user')
const Quote = require('../models/quote-invoice/quote')
const Invoice = require('../models/quote-invoice/invoice')
const Identity = require('../models/identity/identity')
const ErrorResponse = require('../utils/errorResponse')
const asyncHandler = require('../middleware/async')
const { io } = require('../utils/socket')

const fs = require('fs')
const path = require('path')

exports.getProfiles = asyncHandler(async (req, res, next) => {
  const profiles = await Profile.find({ user: req.id })

  io.to(req.user).emit('allProfiles', {
    status: 'success',
    data: profiles,
  })
})

exports.getProfile = asyncHandler(async (req, res, next) => {
  const profile = await Profile.findById(req.id)

  io.to(req.user).emit('singleProfile', {
    status: 'success',
    data: profile,
  })
})

exports.createProfile = asyncHandler(async (req, res, next) => {
  const profile = await Profile.create(req)

  io.to(req.user).emit('createProfile', {
    status: 'success',
    message: 'Profile Created Successfully',
    data: profile,
  })
})

exports.updateProfile = asyncHandler(async (req, res, next) => {
  const profile = await Profile.findByIdAndUpdate(req.profileId, req.profile, {
    new: true,
    runValidators: true,
  })

  const user = await User.findById(req.id).populate(['profile', {
    path: 'profile',
    populate: [{
      path: 'defaultIdentity',
    }],
  }])

  io.to(req.id).emit('updateProfile', {
    status: 'success',
    message: 'Profile Updated Successfully',
    data: {
      profile: profile,
      user: user,
    },
  })
})

exports.deleteProfile = asyncHandler(async (req, res, next) => {
  await Profile.findByIdAndDelete(req.id)

  io.to(req.user).emit('deleteProfile', {
    status: 'success',
    message: 'Profile Deleted Successfully',
    data: {},
  })
})

exports.uploadProfileImage = asyncHandler(async (req, res, next) => {
  // console.log({ req })

  const user = await User.findById(req.id).populate('profile')
  const profile = await Profile.findById(req.profileId)

  const userFolder = `/uploads/users/${ user._id }/profile`

  const { fileName, profileImg } = req

  if (!profileImg || !profileImg.startsWith('data:image/')) {
    console.log(`${ fileName } is not a valid file`)

    return io.to(user._id).emit('file-upload-error', 'Invalid file data')
  } else {
    console.log('image')
  }

  try {
    // Extract base64 data (removes the prefix)
    const base64Data = profileImg.split(',')[1] // Split at ',' to remove the 'data:image/...' prefix

    if (!base64Data) {
      return io.to(user._id).emit('file-upload-error', 'Base64 data is missing')
    }

    const fileBuffer = Buffer.from(base64Data, 'base64')

    if (!fs.existsSync('./public/uploads/users/' + user._id + '/profile')) {
      fs.mkdirSync('./public/uploads/users/' + user._id + '/profile', { recursive: true })
    }

    const uploadPath = path.join('./public', userFolder, req.fileName)

    fs.writeFileSync(uploadPath, fileBuffer, err => {
      if (err) {
        console.error('Error saving file:', err)

        return io.to(req.id).emit('file-upload-error', 'Error saving the file')
      }

      io.to(req.id).emit('file-upload-complete', 'File uploaded successfully')
    })

    let progress = 0

    const interval = setInterval(() => {
      progress += 10

      io.to(req.id).emit('file-upload-progress', progress)

      console.log('progress', progress)

      if (progress >= 100) {
        clearInterval(interval)
      }
    }, 100)

  } catch (err) {
    console.error('Error processing file:', err)
    io.to(req.id).emit('file-upload-error', 'Error processing file')
  }

  const profileUpdated = await Profile.findByIdAndUpdate(profile, {
    profileImg: req.fileName,
    profileImgUrl: userFolder,
  }, {
    new: true,
    runValidators: true,
  })

  console.log('profile', { profileUpdated })

  const userRefreshed = await User.findById(user._id).populate('profile')

  io.to(req.id).emit('uploadProfileImage', {
    status: 'success',
    message: 'Profile Image Uploaded Successfully!',
    user: userRefreshed,
  })
})

exports.uploadCoverImage = asyncHandler(async (req, res, next) => {
  console.log({ req })

  const user = await User.findById(req.id).populate('profile')
  const profile = await Profile.findById(req.profileId)

  const userFolder = `/uploads/users/${ user._id }/cover`

  const { fileName, coverImg } = req

  if (!coverImg || !coverImg.startsWith('data:image/')) {
    console.log(`${ fileName } is not a valid file`)

    return io.to(user._id).emit('file-upload-error-cover', 'Invalid file data')
  } else {
    console.log('image')
  }

  try {
    // Extract base64 data (removes the prefix)
    const base64Data = coverImg.split(',')[1] // Split at ',' to remove the 'data:image/...' prefix

    if (!base64Data) {
      return io.to(user._id).emit('file-upload-error-cover', 'Base64 data is missing')
    }

    const fileBuffer = Buffer.from(base64Data, 'base64')

    if (!fs.existsSync('./public/uploads/users/' + user._id + '/cover')) {
      fs.mkdirSync('./public/uploads/users/' + user._id + '/cover', { recursive: true })
    }

    const uploadPath = path.join('./public', userFolder, req.fileName)

    fs.writeFileSync(uploadPath, fileBuffer, err => {
      if (err) {
        console.error('Error saving file:', err)

        return io.to(req.id).emit('file-upload-error-cover', 'Error saving the file')
      }

      io.to(req.id).emit('file-upload-complete-cover', 'File uploaded successfully')
    })

    let progress = 0

    const interval = setInterval(() => {
      progress += 10

      io.to(req.id).emit('file-upload-progress-cover', progress)

      console.log('progress', progress)

      if (progress >= 100) {
        clearInterval(interval)
      }
    }, 100)

  } catch (err) {
    console.error('Error processing file:', err)
    io.to(req.id).emit('file-upload-error-cover', 'Error processing file')
  }

  const profileUpdated = await Profile.findByIdAndUpdate(profile, {
    coverImg: req.fileName,
    coverImgUrl: userFolder,
  }, {
    new: true,
    runValidators: true,
  })

  console.log('profile', { profileUpdated })

  const userRefreshed = await User.findById(user._id).populate('profile')

  io.to(req.id).emit('uploadCoverImage', {
    status: 'success',
    message: 'Cover Image Uploaded Successfully!',
    user: userRefreshed,
  })
})

exports.activity = asyncHandler(async (req, res, next) => {
  const sevenDaysAgo = new Date()

  sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)

  // Fetch the quotes from the last 7 days
  const last7DaysQuotes = await Quote.find({
    createdAt: { $gte: sevenDaysAgo },
  }).sort({ createdAt: -1 })

  // Fetch the invoices from the last 7 days
  const last7DaysInvoices = await Invoice.find({
    createdAt: { $gte: sevenDaysAgo },
  }).sort({ createdAt: -1 })

  // Combine the results from both collections
  const combinedActivities = [...last7DaysQuotes, ...last7DaysInvoices]

  // Sort the combined activities by createdAt in descending order
  combinedActivities.sort((a, b) => b.createdAt - a.createdAt)

  // Return the top 10 most recent activities
  const latestActivities = combinedActivities.slice(0, 10)

  io.to(req.user).emit('activity', {
    status: 'success',
    message: 'Activity Loaded Successfully!',
    data: latestActivities,
  })
})
