const Identity = require('../../models/identity/identity')
const User = require('../../models/user/user')
const ErrorResponse = require('../../utils/errorResponse')
const asyncHandler = require('../../middleware/async')
const { Socket, io } = require('../../utils/socket')
const fs = require('fs')
const path = require('path')

exports.getIdentities = asyncHandler(async (req, res, next) => {
  const identities = await Identity.find({ user: req.user })

  io.to(req.user).emit('allIdentities', {
    status: 'success',
    data: identities,
  })
})

exports.getIdentity = asyncHandler(async (req, res, next) => {
  const identity = await Identity.find({ user: req.user, identity: req.id })

  io.to(req.user).emit('singleIdentity', {
    status: 'success',
    data: identity,
  })
})

exports.createFromOnboarding = asyncHandler(async (req, res, next) => {
  console.log('Onboarding request', { req })

  // Check if user can create more identities
  const user = await User.findById(req.userId || req.user)
  if (!user.canCreateIdentity()) {
    return io.to(req.userId || req.user).emit('createFromOnboarding', {
      status: 'error',
      message: `Maximum identities limit reached (${ user.maxIdentities }). ${ user.maxIdentitiesOverride ? 'Contact admin to increase your limit.' : 'Upgrade your subscription to create more identities.' }`,
      data: {
        maxIdentities: user.maxIdentities,
        currentCount: user.identities ? user.identities.length : 0,
        remainingSlots: user.getRemainingIdentitySlots(),
        maxIdentitiesOverride: user.maxIdentitiesOverride,
      },
    })
  }

  const {
    name,
    description,
    logo,
    filename,
    type,
    website,
    primaryEmail,
    email,
    contactNumber,
    streetAddress,
    city,
    state,
    zipCode,
    country,
    status,
    industryType,
    userId,
  } = req

  const identity = await Identity.create({
    name,
    description,
    logo: filename,
    website,
    primaryEmail,
    identityAddress: {
      streetAddress,
      city,
      state,
      zipCode,
      country,
      email,
      contactNumber,
    },
    status,
    industryType,
    user: userId,
  })

  const updatedUser = await User.findByIdAndUpdate(userId, {
    $push: {
      identities: identity,
    },
    newUser: false,
  }, {
    new: true,
    runValidators: true,
  })

  console.log('user', { updatedUser })

  const fileName = filename

  const identitiesFolder = `/uploads/users/${ userId }/identities`

  if (!type || !type.startsWith('image/')) {
    console.log(`${ fileName } is not a valid file`)

    return io.to(userId).emit('file-upload-error', 'Invalid file data')
  } else {
    console.log('image')
  }

  try {
    // Extract base64 data (removes the prefix)
    const base64Data = logo

    if (!base64Data) {
      return io.to(userId).emit('file-upload-error', 'Base64 data is missing')
    }

    const fileBuffer = Buffer.from(base64Data, 'base64')

    if (!fs.existsSync('./public/uploads/users/' + userId + '/identities')) {
      fs.mkdirSync('./public/uploads/users/' + userId + '/identities', { recursive: true })
    }

    const uploadPath = path.join('./public', identitiesFolder, fileName)

    fs.writeFileSync(uploadPath, fileBuffer, err => {
      if (err) {
        console.error('Error saving file:', err)

        return io.to(userId).emit('file-upload-error', 'Error saving the file')
      }

      io.to(userId).emit('file-upload-complete', 'File uploaded successfully')
    })

    let progress = 0

    const interval = setInterval(() => {
      progress += 10

      io.to(userId).emit('file-upload-progress', progress)

      console.log('progress', progress)

      if (progress >= 100) {
        clearInterval(interval)
      }
    }, 100)

  } catch (err) {
    console.error('Error processing file:', err)
    io.to(userId).emit('file-upload-error', 'Error processing file')
  }

  io.to(userId).emit('createOnboardIdentity', {
    status: 'success',
    message: 'Identity successfully created!',
    data: identity,
  })
})

// CREATE
exports.createIdentity = asyncHandler(async (req, res) => {
  console.log('createIdentity', { req })

  // Check if user can create more identities
  const user = await User.findById(req.user)
  if (!user.canCreateIdentity()) {
    return io.to(req.user).emit('createIdentity', {
      status: 'error',
      message: `Maximum identities limit reached (${ user.maxIdentities }). ${ user.maxIdentitiesOverride ? 'Contact admin to increase your limit.' : 'Upgrade your subscription to create more identities.' }`,
      data: {
        maxIdentities: user.maxIdentities,
        currentCount: user.identities ? user.identities.length : 0,
        remainingSlots: user.getRemainingIdentitySlots(),
        maxIdentitiesOverride: user.maxIdentitiesOverride,
      },
    })
  }

  if (!req.filename) {
    const {
      name,
      description,
      website,
      primaryEmail,
      email,
      contactNumber,
      streetAddress,
      city,
      state,
      zipCode,
      country,
      status,
      industryType,
      userId,
    } = req

    const identity = await Identity.create({
      name,
      description,
      website,
      primaryEmail,
      identityAddress: {
        streetAddress,
        city,
        state,
        zipCode,
        country,
        email,
        contactNumber,
      },
      status,
      industryType,
      user: userId,
    })

    await User.findByIdAndUpdate(userId, {
      $push: { identities: identity._id },
      newUser: false,
    })
  } else {
    const {
      name,
      description,
      logo,
      filename,
      type,
      website,
      primaryEmail,
      email,
      contactNumber,
      streetAddress,
      city,
      state,
      zipCode,
      country,
      status,
      industryType,
      userId,
    } = req

    const identity = await Identity.create({
      name,
      description,
      logo: filename,
      website,
      primaryEmail,
      identityAddress: {
        streetAddress,
        city,
        state,
        zipCode,
        country,
        email,
        contactNumber,
      },
      status,
      industryType,
      user: userId,
    })

    await User.findByIdAndUpdate(userId, {
      $push: { identities: identity._id },
      newUser: false,
    })

    const fileName = filename

    const identitiesFolder = `/uploads/users/${ userId }/identities`

    if (!type || !type.startsWith('image/')) {
      io.to(userId).emit('file-upload-error', 'Invalid file data')
    }

    try {
      // Extract base64 data (removes the prefix)
      const base64Data = logo

      if (!base64Data) {
        return io.to(userId).emit('file-upload-error', 'Base64 data is missing')
      }

      const fileBuffer = Buffer.from(base64Data, 'base64')

      if (!fs.existsSync('./public/uploads/users/' + userId + '/identities')) {
        fs.mkdirSync('./public/uploads/users/' + userId + '/identities', { recursive: true })
      }

      const uploadPath = path.join('./public', identitiesFolder, fileName)

      fs.writeFileSync(uploadPath, fileBuffer, err => {
        if (err) {
          console.error('Error saving file:', err)

          return io.to(userId).emit('file-upload-error', 'Error saving the file')
        }

        io.to(userId).emit('file-upload-complete', 'File uploaded successfully')
      })

      let progress = 0

      const interval = setInterval(() => {
        progress += 10

        io.to(userId).emit('file-upload-progress', progress)

        if (progress >= 100) {
          clearInterval(interval)
          io.to(userId).emit('file-upload-complete', {
            status: 'success',
            message: 'Logo uploaded successfully!',
          })
        }
      }, 100)

    } catch (err) {
      console.error('Error processing file:', err)
      io.to(userId).emit('file-upload-error', 'Error processing file')
    }
  }

  const identities = await Identity.find({ user: req.userId })

  // Send success event
  io.to(req.userId).emit('createIdentity', {
    status: 'success',
    message: 'Identity successfully created!',
    data: identities,
  })
})

// UPDATE
exports.updateIdentity = asyncHandler(async (req, res) => {
  console.log('updateIdentity', { req })

  if (!req.filename) {
    const {
      name,
      description,
      website,
      primaryEmail,
      email,
      contactNumber,
      streetAddress,
      city,
      state,
      zipCode,
      country,
      status,
      industryType,
      userId,
    } = req

    const identity = await Identity.findByIdAndUpdate(req.id, {
      name,
      description,
      website,
      primaryEmail,
      identityAddress: {
        streetAddress,
        city,
        state,
        zipCode,
        country,
        email,
        contactNumber,
      },
      status,
      industryType,
      user: userId,
    }, {
      new: true,
      runValidators: true,
    })
  } else {
    const {
      name,
      description,
      logo,
      filename,
      type,
      website,
      primaryEmail,
      email,
      contactNumber,
      streetAddress,
      city,
      state,
      zipCode,
      country,
      status,
      industryType,
      userId,
    } = req

    const identity = await Identity.findByIdAndUpdate(req.id, {
      name,
      description,
      logo: filename,
      website,
      primaryEmail,
      identityAddress: {
        streetAddress,
        city,
        state,
        zipCode,
        country,
        email,
        contactNumber,
      },
      status,
      industryType,
      user: userId,
    }, {
      new: true,
      runValidators: true,
    })

    const fileName = filename

    const identitiesFolder = `/uploads/users/${ userId }/identities`

    if (!type || !type.startsWith('image/')) {
      io.to(userId).emit('file-upload-error', 'Invalid file data')
    }

    try {
      // Extract base64 data (removes the prefix)
      const base64Data = logo

      if (!base64Data) {
        return io.to(userId).emit('file-upload-error', 'Base64 data is missing')
      }

      const fileBuffer = Buffer.from(base64Data, 'base64')

      if (!fs.existsSync('./public/uploads/users/' + userId + '/identities')) {
        fs.mkdirSync('./public/uploads/users/' + userId + '/identities', { recursive: true })
      }

      const uploadPath = path.join('./public', identitiesFolder, fileName)

      fs.writeFileSync(uploadPath, fileBuffer, err => {
        if (err) {
          console.error('Error saving file:', err)

          return io.to(userId).emit('file-upload-error', 'Error saving the file')
        }

        io.to(userId).emit('file-upload-complete', 'File uploaded successfully')
      })

      let progress = 0

      const interval = setInterval(() => {
        progress += 10

        io.to(userId).emit('file-upload-progress', progress)

        if (progress >= 100) {
          clearInterval(interval)
          io.to(userId).emit('file-upload-complete', {
            status: 'success',
            message: 'Logo uploaded successfully!',
          })
        }
      }, 100)

    } catch (err) {
      console.error('Error processing file:', err)
      io.to(userId).emit('file-upload-error', 'Error processing file')
    }
  }

  const identities = await Identity.find({ user: req.userId })

  io.to(req.userId).emit('updateIdentity', {
    status: 'success',
    message: 'Identity successfully updated!',
    data: identities,
  })
})

// DELETE
exports.deleteIdentity = asyncHandler(async (req, res) => {
  console.log('deleteIdentity', { req })

  const identityId = req.id

  const identity = await Identity.findByIdAndDelete(identityId)

  if (!identity) {
    io.to(req.user).emit('deleteIdentity', {
      status: 'error',
      message: 'Identity Not Found!',
      data: identity,
    })
  } else {
    // Optionally remove from User as well
    const updatedUser = await User.findByIdAndUpdate(req.user, {
      $pull: { identities: identity._id },
    })

    console.log({ updatedUser })

    const identities = await Identity.find({ user: req.user })

    io.to(req.user).emit('deleteIdentity', {
      status: 'success',
      message: 'Identity successfully deleted!',
      data: identities,
    })
  }
})