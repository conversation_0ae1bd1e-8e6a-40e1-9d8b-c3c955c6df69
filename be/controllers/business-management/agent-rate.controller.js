const AgentRate = require('../../models/business-management/agent-rate')
const User = require('../../models/user/user')
const Identity = require('../../models/identity/identity')
const ErrorResponse = require('../../utils/errorResponse')
const asyncHandler = require('../../middleware/async')
const { io } = require('../../utils/socket')

exports.getAgentRates = asyncHandler(async (req, res, next) => {
  const agentRates = await AgentRate.find({ user: req.user }).populate('identity')

  io.to(req.user).emit('allAgentRates', {
    status: 'success',
    data: agentRates,
  })
})

exports.getAgentRate = asyncHandler(async (req, res, next) => {
  const agentRate = await AgentRate.findById(req.id)

  io.to(req.user).emit('singleAgentRate', {
    status: 'success',
    data: agentRate,
  })
})

exports.createAgentRate = asyncHandler(async (req, res, next) => {
  console.log({ req })
  try {
    /*const checkIdentity = await Identity.findOne({
                  _id: req.identity,
                  agentRate: { $exists: true, $ne: null },
                })

                console.log({ checkIdentity })

                if (checkIdentity) {
                  io.to(req.user).emit('createAgentRate', {
                    status: 'error',
                    message: 'Agent Rate Already Assigned To Identity',
                  })
                } else {*/
    const agentRate = await AgentRate.create(req)

    await Identity.findByIdAndUpdate(req.identity, {
      $push: { agentRate },
    })

    const agentRates = await AgentRate.find({ user: req.user }).populate('identity')

    io.to(req.user).emit('createAgentRate', {
      status: 'success',
      message: 'Agent Rate Created Successfully',
      data: {
        agentRates,
        agentRate,
      },
    })

    // }
  } catch (error) {
    io.to(req.user).emit('createAgentRate', {
      status: 'error',
      message: error.message,
    })
  }
})

exports.updateAgentRate = asyncHandler(async (req, res, next) => {
  console.log(req.rates)
  try {
    const agentRate = await AgentRate.findByIdAndUpdate(req.id, req, {
      new: true,
      runValidators: true,
    })

    const agentRates = await AgentRate.find({ user: req.user }).populate('identity')

    io.to(req.user).emit('updateAgentRate', {
      status: 'success',
      message: 'Agent Rate Updated Successfully',
      data: {
        agentRates,
        agentRate,
      },
    })
  } catch (error) {
    io.to(req.user).emit('updateAgentRate', {
      status: 'error',
      message: error.message,
    })
  }
})

exports.deleteAgentRate = asyncHandler(async (req, res, next) => {
  console.log({ req })
  try {
    const agentRate = await AgentRate.findByIdAndDelete(req.id)

    if (!agentRate) {
      io.to(req.user).emit('deleteAgentRate', {
        status: 'error',
        message: 'Agent Rate Not Found',
      })
    } else {
      const updateIdentity = await Identity.findByIdAndUpdate(req.identity, {
        $pull: { agentRate: agentRate._id },
      })

      console.log({ updateIdentity })

      const agentRates = await AgentRate.find({ user: req.user }).populate('identity')

      io.to(req.user).emit('deleteAgentRate', {
        status: 'success',
        message: 'Agent Rate Deleted Successfully',
        data: agentRates,
      })
    }
  } catch (error) {
    io.to(req.user).emit('deleteAgentRate', {
      status: 'error',
      message: error.message,
    })
  }
})