const SubCategory = require('../../models/line-item-group/sub-category')
const ErrorResponse = require('../../utils/errorResponse')
const asyncHandler = require('../../middleware/async')
const { io } = require('../../utils/socket')

exports.getSubCategories = asyncHandler(async (req, res, next) => {
  const subCategories = await SubCategory.find()

  io.to(req.user).emit('allSubCategories', {
    status: 'success',
    data: subCategories,
  })
})

exports.getSubCategory = asyncHandler(async (req, res, next, io) => {
  const subCategory = await SubCategory.findById(req.id).populate(['categoryId'])

  io.to(req.user).emit('singleSubCategory', {
    status: 'success',
    data: subCategory,
  })
})

exports.createSubCategory = asyncHandler(async (req, res, next) => {
  const subCategory = await SubCategory.create(req)

  io.to(req.user).emit('createSubCategory', {
    status: 'success',
    message: 'Sub Category Created Successfully',
    data: subCategory,
  })
})

exports.updateSubCategory = asyncHandler(async (req, res, next) => {
  const subCategory = await SubCategory.findByIdAndUpdate(req.id, req, {
    new: true,
    runValidators: true,
  })

  io.to(req.user).emit('updateSubCategory', {
    status: 'success',
    message: 'Sub Category Updated Successfully',
    data: subCategory,
  })
})

exports.deleteSubCategory = asyncHandler(async (req, res, next) => {
  await SubCategory.findByIdAndDelete(req.params.id)

  io.to(req.user).emit('deleteSubCategory', {
    status: 'success',
    message: 'Sub Category Deleted Successfully',
    data: {},
  })
})
