const cron = require('node-cron')
const UserSubscription = require('../models/user/user-subscription')
const AppNotification = require('../models/app-notification/app-notification')

// Run every day at 7:00 AM
cron.schedule('0 7 * * *', async () => {
  try {
    const now = new Date()

    // Calculate the date 3 days from now (start of that day)
    const targetDate = new Date(now)

    targetDate.setDate(targetDate.getDate() + 3)
    targetDate.setHours(0, 0, 0, 0)

    const nextDay = new Date(targetDate)

    nextDay.setDate(nextDay.getDate() + 1)

    // Find active subscriptions expiring exactly 3 days from now
    const expiringSubs = await UserSubscription.find({
      endsAt: {
        $gte: targetDate,
        $lt: nextDay,
      },
      status: 'active',
    }).populate('user')

    for (const sub of expiringSubs) {
      await AppNotification.create({
        title: 'Your subscription is expiring soon',
        description: 'Your current plan will expire in 3 days. Renew to avoid interruption.',
        sendToBrowser: true,
        sendToApp: true,
        sendToEmail: true,
        sendWhen: 'online',
        isGlobal: false,
        isRead: false,
        user: [sub.user._id],
      })

      console.log(`Notification sent to user: ${ sub.user.email }`)
    }
  } catch (err) {
    console.error('Error sending expiring subscription notifications:', err)
  }
})
