const mongoose = require('mongoose')

const BankAccountSchema = new mongoose.Schema({
  bankName: {
    type: String,
  },
  accountType: {
    type: String,
  },
  accountNumber: {
    type: String,
  },
  branchCode: {
    type: String,
  },
  accountHolderName: {
    type: String,
  },
  instructions: {
    type: String,
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  identity: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Identity',
    required: true,
  },
})

module.exports = mongoose.model('BankAccount', BankAccountSchema)
