const mongoose = require('mongoose')

const BookmarkSchema = new mongoose.Schema({
  bookmarks: {
    type: [Object],
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
}, {
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
})

module.exports = mongoose.model('Bookmark', BookmarkSchema)
