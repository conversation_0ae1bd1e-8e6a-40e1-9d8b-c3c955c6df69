const mongoose = require('mongoose')

const TermConditionTemplateSchema = new mongoose.Schema({
  templateName: {
    type: String,
    required: true,
    trim: true,
  },
  type: {
    type: String,
    enum: [
      'General',
      'Invoice',
      'Estimate',
      'Quote',
      'Production',
      'AdvanceInvoice',
      'License',
      'Custom',
    ],
    required: true,
  },
  content: {
    type: String,
    required: true,
  },
  variables: [{
    name: String,
    description: String,
    defaultValue: String,
    type: {
      type: String,
      enum: ['text', 'number', 'date', 'boolean'],
      default: 'text',
    },
  }],
  isActive: {
    type: Boolean,
    default: true,
  },
  isDefault: {
    type: Boolean,
    default: false,
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  identity: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Identity',
  },
  tags: [{
    type: String,
    trim: true,
  }],
  version: {
    type: Number,
    default: 1,
  },
  parentTemplate: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'TermConditionTemplate',
  },
  usageCount: {
    type: Number,
    default: 0,
  },
}, {
  timestamps: true,
})

// Indexes for better performance
TermConditionTemplateSchema.index({ user: 1 })
TermConditionTemplateSchema.index({ type: 1 })
TermConditionTemplateSchema.index({ isActive: 1 })
TermConditionTemplateSchema.index({ isDefault: 1 })
TermConditionTemplateSchema.index({ templateName: 'text', content: 'text' })

// Virtual for variable count
TermConditionTemplateSchema.virtual('variableCount').get(function () {
  return this.variables ? this.variables.length : 0
})

// Ensure virtual fields are serialized
TermConditionTemplateSchema.set('toJSON', { virtuals: true })

module.exports = mongoose.model('TermConditionTemplate', TermConditionTemplateSchema)