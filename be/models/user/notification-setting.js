const mongoose = require('mongoose')

const NotificationSettingSchema = new mongoose.Schema({
  recentDevices: {
    type: Array,
  },
  selectedNotification: {
    type: String,
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
})

module.exports = mongoose.model('NotificationSetting', NotificationSettingSchema)
