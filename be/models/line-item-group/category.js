const mongoose = require('mongoose')
const slugify = require('slugify')

const CategorySchema = new mongoose.Schema({
  name: {
    type: String,
  },
  slug: {
    type: String,
    unique: true,
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: false,
  },
  subCategoryId: {
    type: [mongoose.Schema.Types.ObjectId],
    ref: 'SubCategory',
    required: false,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
}, {
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
})

CategorySchema.pre('save', function (next) {
  this.slug = slugify(this.name, { lower: true })
  next()
})

module.exports = mongoose.model('Category', CategorySchema)
