const mongoose = require('mongoose')

const EstimateSchema = new mongoose.Schema({
  name: {
    type: String,
  },
  estimateNumber: {
    type: String,
  },
  revisionNumber: {
    type: String,
  },
  estimateDate: {
    type: Date,
    default: Date.now,
  },
  currency: {
    type: String,
  },
  status: {
    type: String,

    // enum: ['estimate', 'quotation', 'pre', 'production', 'post', 'invoice'],
  },
  items: {
    type: [Object],
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  job: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Job',
    required: true,
  },
  expenses: {
    type: [mongoose.Schema.Types.ObjectId],
    ref: 'Expense',
    required: true,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
}, {
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
})

module.exports = mongoose.model('Estimate', EstimateSchema)
