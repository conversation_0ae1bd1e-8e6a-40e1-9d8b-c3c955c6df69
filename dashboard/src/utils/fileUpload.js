import { toast } from 'vue3-toastify'

// File validation constants
export const FILE_CONSTRAINTS = {
  IMAGE: {
    maxSize: 5 * 1024 * 1024, // 5MB
    allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'],
    allowedExtensions: ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'],
  },
  DOCUMENT: {
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['application/pdf', 'text/plain', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
    allowedExtensions: ['.pdf', '.txt', '.doc', '.docx'],
  },
}

/**
 * Validate file against constraints
 * @param {File} file - The file to validate
 * @param {string} type - The file type constraint ('IMAGE' or 'DOCUMENT')
 * @returns {Object} - Validation result with isValid and error message
 */
export function validateFile(file, type = 'IMAGE') {
  const constraints = FILE_CONSTRAINTS[type]
  
  if (!constraints) {
    return { isValid: false, error: 'Invalid file type constraint' }
  }
  
  // Check file size
  if (file.size > constraints.maxSize) {
    const maxSizeMB = Math.round(constraints.maxSize / (1024 * 1024))
    
    return { 
      isValid: false, 
      error: `File size must be less than ${maxSizeMB}MB`, 
    }
  }
  
  // Check file type
  if (!constraints.allowedTypes.includes(file.type)) {
    return { 
      isValid: false, 
      error: `File type not supported. Allowed types: ${constraints.allowedExtensions.join(', ')}`, 
    }
  }
  
  return { isValid: true, error: null }
}

/**
 * Convert file to base64 data URL
 * @param {File} file - The file to convert
 * @returns {Promise<string>} - Base64 data URL
 */
export function fileToBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()

    reader.readAsDataURL(file)
    reader.onload = () => resolve(reader.result)
    reader.onerror = error => reject(error)
  })
}

/**
 * Convert file to array buffer
 * @param {File} file - The file to convert
 * @returns {Promise<ArrayBuffer>} - Array buffer
 */
export function fileToArrayBuffer(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()

    reader.readAsArrayBuffer(file)
    reader.onload = () => resolve(reader.result)
    reader.onerror = error => reject(error)
  })
}

/**
 * Get image dimensions from file
 * @param {File} file - The image file
 * @returns {Promise<Object>} - Object with width and height
 */
export function getImageDimensions(file) {
  return new Promise((resolve, reject) => {
    const img = new Image()
    const url = URL.createObjectURL(file)
    
    img.onload = () => {
      URL.revokeObjectURL(url)
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight,
      })
    }
    
    img.onerror = () => {
      URL.revokeObjectURL(url)
      reject(new Error('Failed to load image'))
    }
    
    img.src = url
  })
}

/**
 * Resize image file to specified dimensions
 * @param {File} file - The image file
 * @param {number} maxWidth - Maximum width
 * @param {number} maxHeight - Maximum height
 * @param {number} quality - JPEG quality (0-1)
 * @returns {Promise<Blob>} - Resized image blob
 */
export function resizeImage(file, maxWidth = 800, maxHeight = 600, quality = 0.8) {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()
    const url = URL.createObjectURL(file)
    
    img.onload = () => {
      URL.revokeObjectURL(url)
      
      // Calculate new dimensions
      let { width, height } = img
      
      if (width > height) {
        if (width > maxWidth) {
          height = (height * maxWidth) / width
          width = maxWidth
        }
      } else {
        if (height > maxHeight) {
          width = (width * maxHeight) / height
          height = maxHeight
        }
      }
      
      // Set canvas dimensions
      canvas.width = width
      canvas.height = height
      
      // Draw and resize image
      ctx.drawImage(img, 0, 0, width, height)
      
      // Convert to blob
      canvas.toBlob(resolve, file.type, quality)
    }
    
    img.onerror = () => {
      URL.revokeObjectURL(url)
      reject(new Error('Failed to load image for resizing'))
    }
    
    img.src = url
  })
}

/**
 * Upload file to server (placeholder implementation)
 * In a real application, this would upload to your file server
 * @param {File} file - The file to upload
 * @param {Object} options - Upload options
 * @returns {Promise<Object>} - Upload result with URL
 */
export async function uploadFileToServer(file, options = {}) {
  // This is a placeholder implementation
  // In a real app, you would upload to your server or cloud storage
  
  try {
    // For now, we'll just convert to base64
    // In production, you'd use FormData and send to your upload endpoint
    const base64 = await fileToBase64(file)
    
    // Simulate upload delay
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Return mock response
    return {
      success: true,
      url: base64, // In production, this would be the server URL
      filename: file.name,
      size: file.size,
      type: file.type,
    }
  } catch (error) {
    throw new Error('Upload failed: ' + error.message)
  }
}

/**
 * Handle multiple file uploads
 * @param {FileList} files - The files to upload
 * @param {Object} options - Upload options
 * @returns {Promise<Array>} - Array of upload results
 */
export async function uploadMultipleFiles(files, options = {}) {
  const results = []
  
  for (const file of files) {
    try {
      const result = await uploadFileToServer(file, options)

      results.push({ file, result, success: true })
    } catch (error) {
      results.push({ file, error: error.message, success: false })
    }
  }
  
  return results
}

/**
 * Format file size for display
 * @param {number} bytes - File size in bytes
 * @returns {string} - Formatted file size
 */
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * Get file extension from filename
 * @param {string} filename - The filename
 * @returns {string} - File extension
 */
export function getFileExtension(filename) {
  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2)
}

/**
 * Check if file is an image
 * @param {File} file - The file to check
 * @returns {boolean} - True if file is an image
 */
export function isImageFile(file) {
  return file.type.startsWith('image/')
}

/**
 * Generate thumbnail for image file
 * @param {File} file - The image file
 * @param {number} size - Thumbnail size (square)
 * @returns {Promise<string>} - Base64 thumbnail
 */
export async function generateThumbnail(file, size = 150) {
  if (!isImageFile(file)) {
    throw new Error('File is not an image')
  }

  const resized = await resizeImage(file, size, size, 0.7)
  
  return fileToBase64(resized)
}

/**
 * Validate multiple files
 * @param {FileList|Array} files - Files to validate
 * @param {string} type - File type category (IMAGE, DOCUMENT, VIDEO)
 * @param {number} maxFiles - Maximum number of files allowed
 * @returns {Object} Validation result with valid files and errors
 */
export function validateMultipleFiles(files, type = 'IMAGE', maxFiles = 10) {
  const fileArray = Array.from(files)
  const validFiles = []
  const errors = []

  if (fileArray.length > maxFiles) {
    return {
      validFiles: [],
      errors: [`Too many files. Maximum allowed: ${maxFiles}`],
    }
  }

  fileArray.forEach((file, index) => {
    const validation = validateFile(file, type)
    if (validation.isValid) {
      validFiles.push(file)
    } else {
      errors.push(`File ${index + 1} (${file.name}): ${validation.error}`)
    }
  })

  return { validFiles, errors }
}

// File type configurations for validation
export const FILE_TYPES = {
  IMAGE: {
    extensions: ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'],
    mimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'],
    maxSize: 5 * 1024 * 1024, // 5MB
    description: 'Images (JPG, PNG, GIF, WebP, SVG)',
  },
  DOCUMENT: {
    extensions: ['pdf', 'doc', 'docx', 'txt'],
    mimeTypes: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain'],
    maxSize: 10 * 1024 * 1024, // 10MB
    description: 'Documents (PDF, DOC, DOCX, TXT)',
  },
  VIDEO: {
    extensions: ['mp4', 'mov', 'avi', 'mkv', 'webm'],
    mimeTypes: ['video/mp4', 'video/quicktime', 'video/x-msvideo', 'video/x-matroska', 'video/webm'],
    maxSize: 100 * 1024 * 1024, // 100MB
    description: 'Videos (MP4, MOV, AVI, MKV, WebM)',
  },
}
