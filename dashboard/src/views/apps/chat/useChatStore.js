import { defineStore } from 'pinia'
import { socket } from '@socket/socket'
import { ref } from 'vue'

export const useChatStore = defineStore('chat', () => {
  const chats = ref([])
  const activeChat = ref(null)
  const messages = ref([])
  const contacts = ref([])
  const profileUser = ref(null)

  const pagination = ref({
    page: 1,
    itemsPerPage: 20,
    total: 0,
  })

  function $reset() {
    chats.value = []
    activeChat.value = null
    messages.value = []
    contacts.value = []
    profileUser.value = null
    pagination.value = {
      page: 1,
      itemsPerPage: 20,
      total: 0,
    }
  }

  // Chat methods
  function getChats(data) {
    console.log('Store: getChats called with:', data)
    socket.emit('chatController:getChats', data)
  }

  function getChat(data) {
    console.log('Store: getChat called with:', data)
    socket.emit('chatController:getChat', data)
  }

  function createIndividualChat(data) {
    console.log('Store: createIndividualChat called with:', data)
    socket.emit('chatController:createIndividualChat', data)
  }

  function createGroupChat(data) {
    console.log('Store: createGroupChat called with:', data)
    socket.emit('chatController:createGroupChat', data)
  }

  function updateChat(data) {
    console.log('Store: updateChat called with:', data)
    socket.emit('chatController:updateChat', data)
  }

  function leaveChat(data) {
    console.log('Store: leaveChat called with:', data)
    socket.emit('chatController:leaveChat', data)
  }

  function addParticipants(data) {
    console.log('Store: addParticipants called with:', data)
    socket.emit('chatController:addParticipants', data)
  }

  // Message methods
  function sendMessage(data) {
    console.log('Store: sendMessage called with:', data)
    socket.emit('messageController:sendMessage', data)
  }

  function getMessages(data) {
    console.log('Store: getMessages called with:', data)
    socket.emit('messageController:getMessages', data)
  }

  function editMessage(data) {
    console.log('Store: editMessage called with:', data)
    socket.emit('messageController:editMessage', data)
  }

  function deleteMessage(data) {
    console.log('Store: deleteMessage called with:', data)
    socket.emit('messageController:deleteMessage', data)
  }

  function addReaction(data) {
    console.log('Store: addReaction called with:', data)
    socket.emit('messageController:addReaction', data)
  }

  function removeReaction(data) {
    console.log('Store: removeReaction called with:', data)
    socket.emit('messageController:removeReaction', data)
  }

  function markMessagesAsRead(data) {
    console.log('Store: markMessagesAsRead called with:', data)
    socket.emit('messageController:markMessagesAsRead', data)
  }

  // Group methods
  function inviteToGroup(data) {
    console.log('Store: inviteToGroup called with:', data)
    socket.emit('groupController:inviteToGroup', data)
  }

  function acceptGroupInvite(data) {
    console.log('Store: acceptGroupInvite called with:', data)
    socket.emit('groupController:acceptGroupInvite', data)
  }

  function rejectGroupInvite(data) {
    console.log('Store: rejectGroupInvite called with:', data)
    socket.emit('groupController:rejectGroupInvite', data)
  }

  function getGroupInvites(data) {
    console.log('Store: getGroupInvites called with:', data)
    socket.emit('groupController:getGroupInvites', data)
  }

  function cancelGroupInvite(data) {
    console.log('Store: cancelGroupInvite called with:', data)
    socket.emit('groupController:cancelGroupInvite', data)
  }

  // Legacy methods for compatibility
  function fetchChatsAndContacts(q) {
    getChats({ search: q })
  }

  function sendMsg(message) {
    if (activeChat.value) {
      sendMessage({
        chatId: activeChat.value._id,
        content: message,
        type: 'text',
      })
    }
  }

  return {
    chats,
    activeChat,
    messages,
    contacts,
    profileUser,
    pagination,
    $reset,
    getChats,
    getChat,
    createIndividualChat,
    createGroupChat,
    updateChat,
    leaveChat,
    addParticipants,
    sendMessage,
    getMessages,
    editMessage,
    deleteMessage,
    addReaction,
    removeReaction,
    markMessagesAsRead,
    inviteToGroup,
    acceptGroupInvite,
    rejectGroupInvite,
    getGroupInvites,
    cancelGroupInvite,

    // Legacy methods
    fetchChatsAndContacts,
    sendMsg,

    // Legacy computed for compatibility
    get chatsContacts() {
      return chats.value || []
    },
  }
}, {
  persistedState: {
    persist: true,
    storage: sessionStorage,
  },
})
