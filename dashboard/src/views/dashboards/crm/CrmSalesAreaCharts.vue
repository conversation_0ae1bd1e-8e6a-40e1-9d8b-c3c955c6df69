<script setup>
import { useTheme } from 'vuetify'

const props = defineProps({
  data: {
    type: Array,
  },
  total: {
    type: [String, Number],
  },
  percentage: {
    type: [String, Number],
  },
})

// Beautiful Color Palette
const chartColorPalette = {
  purple: '#C5A2FF',
  blue: '#4CF3EA',
  yellow: '#FED56A',
  green: '#76FFCE',
  red: '#E57B7B',
  pink: '#F51BC5',
}

const vuetifyTheme = useTheme()
const currentTheme = vuetifyTheme.current.value.colors

const chartOptions = {
  chart: {
    type: 'area',
    parentHeightOffset: 0,
    toolbar: { show: false },
    sparkline: { enabled: true },
  },
  markers: {
    colors: 'transparent',
    strokeColors: 'transparent',
  },
  grid: { show: false },
  colors: [chartColorPalette.green],
  fill: {
    type: 'gradient',
    gradient: {
      shadeIntensity: 0.9,
      opacityFrom: 0.5,
      opacityTo: 0.07,
      stops: [
        0,
        80,
        100,
      ],
    },
  },
  dataLabels: { enabled: false },
  stroke: {
    width: 2,
    curve: 'smooth',
  },
  xaxis: {
    show: true,
    lines: { show: false },
    labels: { show: false },
    stroke: { width: 0 },
    axisBorder: { show: false },
  },
  yaxis: {
    stroke: { width: 0 },
    show: false,
  },
  tooltip: { enabled: false },
}
</script>

<template>
  <VCard>
    <VCardItem class="pb-3">
      <VCardTitle>
        Sales
      </VCardTitle>
      <VCardSubtitle>
        Last Year
      </VCardSubtitle>
    </VCardItem>

    <VueApexCharts
      :options="chartOptions"
      :series="props.data"
      :height="68"
    />

    <VCardText
      v-if="props.total"
      class="pt-1"
    >
      <div class="d-flex align-center justify-space-between gap-x-2">
        <h4 class="text-h4 text-center">
          {{ total }}
        </h4>
        <span
          v-if="props.total.includes('-')"
          class="text-sm text-error"
        >
          {{ props.percentage.toFixed(2) }}%
        </span>
        <span
          v-else
          class="text-sm text-success"
        >
          {{ props.percentage.toFixed(2) }}%
        </span>
      </div>
    </VCardText>
  </VCard>
</template>
