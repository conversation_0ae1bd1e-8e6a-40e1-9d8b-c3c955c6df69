<script setup>
import { ref, computed, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useSocketStore } from '@stores/auth'
import { useAdminIdentityManagementStore } from '@stores/admin/identity-management'
import EditUserMaxIdentitiesDialog from './EditUserMaxIdentitiesDialog.vue'

const authStore = useSocketStore()
const adminStore = useAdminIdentityManagementStore()

const { user } = storeToRefs(authStore)
const { approachingLimits, loading } = storeToRefs(adminStore)

// Reactive data
const threshold = ref(80)
const selectedUser = ref(null)
const isEditDialog = ref(false)
const selectedAlertType = ref('all')

// Computed
const alertTypeOptions = [
  { title: 'All Alerts', value: 'all', color: 'primary' },
  { title: 'Near Limit (80-99%)', value: 'nearLimit', color: 'warning' },
  { title: 'At Limit (100%)', value: 'atLimit', color: 'error' },
  { title: 'Over Limit', value: 'overLimit', color: 'error' },
]

const filteredAlerts = computed(() => {
  if (!approachingLimits.value) return []
  
  const alerts = approachingLimits.value.alerts
  if (selectedAlertType.value === 'all') {
    return [
      ...alerts.nearLimit,
      ...alerts.atLimit,
      ...alerts.overLimit,
    ]
  }
  
  return alerts[selectedAlertType.value] || []
})

const alertSummary = computed(() => {
  if (!approachingLimits.value) return null
  
  return approachingLimits.value.alerts.summary
})

// Methods
const loadAlerts = () => {
  adminStore.getUsersApproachingLimits({
    user: user.value._id,
    threshold: threshold.value,
  })
}

const editUserMaxIdentities = userData => {
  selectedUser.value = userData
  isEditDialog.value = true
}

const getAlertTypeColor = usagePercentage => {
  if (usagePercentage > 100) return 'error'
  if (usagePercentage === 100) return 'error'
  if (usagePercentage >= 80) return 'warning'
  
  return 'primary'
}

const getAlertTypeText = usagePercentage => {
  if (usagePercentage > 100) return 'Over Limit'
  if (usagePercentage === 100) return 'At Limit'
  if (usagePercentage >= 80) return 'Near Limit'
  
  return 'Normal'
}

const formatDate = date => {
  return new Date(date).toLocaleDateString()
}

const quickIncreaseLimit = (userData, increase = 5) => {
  const newLimit = userData.maxIdentities + increase

  adminStore.updateUserMaxIdentities({
    user: user.value._id,
    id: userData.userId,
    maxIdentities: newLimit,
    isOverride: true,
  })
}

// Load alerts on mount
onMounted(() => {
  loadAlerts()
})
</script>

<template>
  <VCardText>
    <!-- Controls -->
    <VRow class="mb-6">
      <VCol
        cols="12"
        md="4"
      >
        <AppTextField
          v-model.number="threshold"
          label="Alert Threshold (%)"
          type="number"
          min="50"
          max="100"
          hint="Show users with usage >= this percentage"
          persistent-hint
          @change="loadAlerts"
        />
      </VCol>
      <VCol
        cols="12"
        md="4"
      >
        <AppSelect
          v-model="selectedAlertType"
          label="Alert Type"
          :items="alertTypeOptions"
          item-title="title"
          item-value="value"
        />
      </VCol>
      <VCol
        cols="12"
        md="4"
        class="d-flex align-end"
      >
        <VBtn
          variant="outlined"
          :loading="loading"
          block
          @click="loadAlerts"
        >
          <VIcon
            start
            icon="tabler-refresh"
          />
          Refresh Alerts
        </VBtn>
      </VCol>
    </VRow>

    <!-- Alert Summary -->
    <VRow
      v-if="alertSummary"
      class="mb-6"
    >
      <VCol
        cols="12"
        md="3"
      >
        <VCard
          color="primary"
          variant="tonal"
        >
          <VCardText class="text-center">
            <VIcon
              icon="tabler-alert-triangle"
              size="32"
              class="mb-2"
            />
            <h4 class="text-h4 mb-1">
              {{ alertSummary.totalAlerts }}
            </h4>
            <p class="text-body-2 mb-0">
              Total Alerts
            </p>
          </VCardText>
        </VCard>
      </VCol>
      
      <VCol
        cols="12"
        md="3"
      >
        <VCard
          color="warning"
          variant="tonal"
        >
          <VCardText class="text-center">
            <VIcon
              icon="tabler-clock-exclamation"
              size="32"
              class="mb-2"
            />
            <h4 class="text-h4 mb-1">
              {{ alertSummary.nearLimitCount }}
            </h4>
            <p class="text-body-2 mb-0">
              Near Limit
            </p>
          </VCardText>
        </VCard>
      </VCol>
      
      <VCol
        cols="12"
        md="3"
      >
        <VCard
          color="error"
          variant="tonal"
        >
          <VCardText class="text-center">
            <VIcon
              icon="tabler-ban"
              size="32"
              class="mb-2"
            />
            <h4 class="text-h4 mb-1">
              {{ alertSummary.atLimitCount }}
            </h4>
            <p class="text-body-2 mb-0">
              At Limit
            </p>
          </VCardText>
        </VCard>
      </VCol>
      
      <VCol
        cols="12"
        md="3"
      >
        <VCard
          color="error"
          variant="tonal"
        >
          <VCardText class="text-center">
            <VIcon
              icon="tabler-exclamation-circle"
              size="32"
              class="mb-2"
            />
            <h4 class="text-h4 mb-1">
              {{ alertSummary.overLimitCount }}
            </h4>
            <p class="text-body-2 mb-0">
              Over Limit
            </p>
          </VCardText>
        </VCard>
      </VCol>
    </VRow>

    <!-- Loading State -->
    <div
      v-if="loading"
      class="text-center py-12"
    >
      <VProgressCircular
        indeterminate
        color="primary"
        size="64"
      />
      <p class="text-body-1 mt-4">
        Loading alerts...
      </p>
    </div>

    <!-- Alerts List -->
    <div v-else-if="filteredAlerts.length > 0">
      <VCard
        v-for="alert in filteredAlerts"
        :key="alert.userId"
        class="mb-4"
        variant="outlined"
      >
        <VCardText>
          <VRow align="center">
            <VCol
              cols="12"
              md="6"
            >
              <div class="d-flex align-center">
                <VAvatar
                  size="50"
                  class="me-4"
                >
                  <VImg
                    v-if="alert.avatar"
                    :src="alert.avatar"
                    :alt="alert.username"
                  />
                  <span v-else>{{ avatarText(alert.username) }}</span>
                </VAvatar>
                <div>
                  <h6 class="text-h6 mb-1">
                    {{ alert.username }}
                  </h6>
                  <p class="text-body-2 mb-1">
                    {{ alert.email }}
                  </p>
                  <VChip
                    size="small"
                    :color="alert.hasOverride ? 'warning' : 'primary'"
                    variant="outlined"
                  >
                    {{ alert.subscriptionPlan }}
                    {{ alert.hasOverride ? '(Override)' : '' }}
                  </VChip>
                </div>
              </div>
            </VCol>

            <VCol
              cols="12"
              md="3"
            >
              <div class="text-center">
                <VProgressCircular
                  :model-value="alert.usagePercentage"
                  :color="getAlertTypeColor(alert.usagePercentage)"
                  size="60"
                  width="6"
                >
                  <span class="text-h6">{{ alert.usagePercentage }}%</span>
                </VProgressCircular>
                <p class="text-body-2 mt-2 mb-0">
                  {{ alert.identityCount }}/{{ alert.maxIdentities }} identities
                </p>
                <VChip
                  size="small"
                  :color="getAlertTypeColor(alert.usagePercentage)"
                  variant="tonal"
                  class="mt-1"
                >
                  {{ getAlertTypeText(alert.usagePercentage) }}
                </VChip>
              </div>
            </VCol>

            <VCol
              cols="12"
              md="3"
            >
              <div class="d-flex flex-column gap-2">
                <!-- Quick Actions -->
                <VBtn
                  size="small"
                  color="primary"
                  variant="tonal"
                  @click="quickIncreaseLimit(alert, 5)"
                >
                  <VIcon
                    start
                    icon="tabler-plus"
                  />
                  +5 Limit
                </VBtn>
                
                <VBtn
                  size="small"
                  color="primary"
                  variant="outlined"
                  @click="editUserMaxIdentities(alert)"
                >
                  <VIcon
                    start
                    icon="tabler-edit"
                  />
                  Edit Limit
                </VBtn>
              </div>
            </VCol>
          </VRow>

          <!-- Recent Identities -->
          <VRow
            v-if="alert.recentIdentities && alert.recentIdentities.length > 0"
            class="mt-4"
          >
            <VCol cols="12">
              <VDivider class="mb-3" />
              <h6 class="text-h6 mb-2">
                Recent Identities
              </h6>
              <div class="d-flex gap-2">
                <VChip
                  v-for="identity in alert.recentIdentities"
                  :key="identity._id"
                  size="small"
                  variant="outlined"
                >
                  Created {{ formatDate(identity.createdAt) }}
                </VChip>
              </div>
            </VCol>
          </VRow>
        </VCardText>
      </VCard>
    </div>

    <!-- Empty State -->
    <div
      v-else
      class="text-center py-12"
    >
      <VIcon
        icon="tabler-shield-check"
        size="64"
        color="success"
        class="mb-4"
      />
      <h3 class="text-h5 mb-2">
        No Alerts
      </h3>
      <p class="text-body-1">
        {{ selectedAlertType === 'all' 
          ? `No users are approaching their identity limits (${threshold}%+ usage).` 
          : `No users in the "${alertTypeOptions.find(opt => opt.value === selectedAlertType)?.title}" category.` }}
      </p>
      <VBtn
        variant="outlined"
        class="mt-4"
        @click="threshold = Math.max(50, threshold - 10)"
      >
        Lower Threshold to {{ Math.max(50, threshold - 10) }}%
      </VBtn>
    </div>

    <!-- Edit Dialog -->
    <EditUserMaxIdentitiesDialog
      v-model:is-dialog-visible="isEditDialog"
      :user="selectedUser"
    />
  </VCardText>
</template>
