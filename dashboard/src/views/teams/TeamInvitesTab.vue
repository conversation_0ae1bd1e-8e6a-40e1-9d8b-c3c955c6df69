<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { useSocketStore } from '@stores/auth'
import { useSocketTeamInviteStore } from '@stores/teams/invites'
import { socket } from '@socket/socket'
import { toast } from 'vue3-toastify'
import { useTheme } from 'vuetify'

const store = useSocketStore()
const inviteStore = useSocketTeamInviteStore()
const vuetifyTheme = useTheme()

const { user } = storeToRefs(store)
const { teamInvites, sentInvites, receivedInvites, pagination } = storeToRefs(inviteStore)

// Reactive data
const loading = ref(false)
const currentTab = ref('received')
const search = ref('')
const selectedStatus = ref('all')

// Computed
const filteredInvites = computed(() => {
  const invites = currentTab.value === 'sent' ? sentInvites.value : receivedInvites.value
  if (!invites) return []
  
  let filtered = invites

  // Filter by search
  if (search.value) {
    filtered = filtered.filter(invite => 
      invite.team.name.toLowerCase().includes(search.value.toLowerCase()) ||
      invite.inviteeEmail.toLowerCase().includes(search.value.toLowerCase()) ||
      invite.inviteeName?.toLowerCase().includes(search.value.toLowerCase()),
    )
  }

  // Filter by status
  if (selectedStatus.value !== 'all') {
    filtered = filtered.filter(invite => invite.status === selectedStatus.value)
  }

  return filtered
})

const statusOptions = [
  { title: 'All Invites', value: 'all' },
  { title: 'Pending', value: 'pending' },
  { title: 'Accepted', value: 'accepted' },
  { title: 'Rejected', value: 'rejected' },
  { title: 'Expired', value: 'expired' },
]

const tabItems = [
  { title: 'Received', value: 'received', icon: 'tabler-inbox' },
  { title: 'Sent', value: 'sent', icon: 'tabler-send' },
]

// Methods
const loadInvites = () => {
  loading.value = true
  inviteStore.getTeamInvites({
    user: user.value._id,
    type: currentTab.value,
    page: pagination.value.page,
    itemsPerPage: pagination.value.itemsPerPage,
    status: selectedStatus.value === 'all' ? '' : selectedStatus.value,
  })
}

const acceptInvite = invite => {
  inviteStore.acceptTeamInvite({
    user: user.value._id,
    id: invite._id,
  })
}

const rejectInvite = invite => {
  if (confirm(`Are you sure you want to reject the invitation to join "${invite.team.name}"?`)) {
    inviteStore.rejectTeamInvite({
      user: user.value._id,
      id: invite._id,
    })
  }
}

const cancelInvite = invite => {
  if (confirm(`Are you sure you want to cancel the invitation for "${invite.inviteeEmail}"?`)) {
    inviteStore.cancelTeamInvite({
      user: user.value._id,
      id: invite._id,
    })
  }
}

const resendInvite = invite => {
  inviteStore.resendTeamInvite({
    user: user.value._id,
    id: invite._id,
  })
}

const getStatusColor = status => {
  switch (status) {
  case 'pending': return 'warning'
  case 'accepted': return 'success'
  case 'rejected': return 'error'
  case 'expired': return 'secondary'
  default: return 'primary'
  }
}

const formatDate = date => {
  return new Date(date).toLocaleDateString()
}

const isExpired = invite => {
  return new Date() > new Date(invite.expirationDate)
}

// Watch for tab and filter changes
watch([currentTab, search, selectedStatus], () => {
  loadInvites()
}, { debounce: 300 })

// Socket listeners
socket.on('allTeamInvites', data => {
  console.log('Received allTeamInvites:', data)
  loading.value = false
  if (data.status === 'success') {
    if (data.data.type === 'sent') {
      sentInvites.value = data.data.invites
    } else {
      receivedInvites.value = data.data.invites
    }
    pagination.value = {
      page: data.data.page,
      itemsPerPage: data.data.itemsPerPage,
      total: data.data.total,
    }
  } else {
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
  }
})

socket.on('acceptTeamInvite', data => {
  if (data.status === 'success') {
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })
    loadInvites()
  } else {
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
  }
})

socket.on('rejectTeamInvite', data => {
  if (data.status === 'success') {
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })
    loadInvites()
  } else {
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
  }
})

socket.on('cancelTeamInvite', data => {
  if (data.status === 'success') {
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })
    loadInvites()
  } else {
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
  }
})

socket.on('resendTeamInvite', data => {
  if (data.status === 'success') {
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })
    loadInvites()
  } else {
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
  }
})

// Real-time notifications
socket.on('teamInviteReceived', data => {
  if (data.status === 'success') {
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'info',
    })
    if (currentTab.value === 'received') {
      loadInvites()
    }
  }
})

// Load data on mount
onMounted(() => {
  loadInvites()
})
</script>

<template>
  <VCardText>
    <!-- Sub Tabs -->
    <VTabs
      v-model="currentTab"
      class="v-tabs-pill mb-6"
    >
      <VTab
        v-for="item in tabItems"
        :key="item.value"
        :value="item.value"
      >
        <VIcon
          start
          :icon="item.icon"
        />
        {{ item.title }}
      </VTab>
    </VTabs>

    <!-- Filters -->
    <VRow class="mb-6">
      <VCol
        cols="12"
        md="4"
      >
        <AppTextField
          v-model="search"
          placeholder="Search invites..."
          append-inner-icon="tabler-search"
          single-line
          hide-details
          clearable
        />
      </VCol>
      <VCol
        cols="12"
        md="3"
      >
        <AppSelect
          v-model="selectedStatus"
          :items="statusOptions"
          item-title="title"
          item-value="value"
          placeholder="Filter by status"
          hide-details
        />
      </VCol>
      <VCol
        cols="12"
        md="5"
        class="text-md-end"
      >
        <VBtn
          variant="outlined"
          :loading="loading"
          @click="loadInvites"
        >
          <VIcon
            start
            icon="tabler-refresh"
          />
          Refresh
        </VBtn>
      </VCol>
    </VRow>

    <!-- Loading State -->
    <div
      v-if="loading"
      class="text-center py-12"
    >
      <VProgressCircular
        indeterminate
        color="primary"
        size="64"
      />
      <p class="text-body-1 mt-4">
        Loading invites...
      </p>
    </div>

    <!-- Empty State -->
    <div
      v-else-if="!filteredInvites || filteredInvites.length === 0"
      class="text-center py-12"
    >
      <VIcon
        :icon="currentTab === 'sent' ? 'tabler-send' : 'tabler-inbox'"
        size="64"
        color="disabled"
        class="mb-4"
      />
      <h3 class="text-h5 mb-2">
        No invites found
      </h3>
      <p class="text-body-1">
        {{ search || selectedStatus !== 'all' ? 'No invites match your current filters.' : 
          currentTab === 'sent' ? 'You haven\'t sent any team invitations yet.' : 'You don\'t have any team invitations.' }}
      </p>
    </div>

    <!-- Invites List -->
    <div v-else>
      <VCard
        v-for="invite in filteredInvites"
        :key="invite._id"
        class="mb-4"
        variant="outlined"
      >
        <VCardText>
          <VRow align="center">
            <VCol
              cols="12"
              md="6"
            >
              <div class="d-flex align-center mb-2">
                <VAvatar
                  size="40"
                  color="primary"
                  variant="tonal"
                  class="me-3"
                >
                  <VIcon icon="tabler-users" />
                </VAvatar>
                <div>
                  <h6 class="text-h6 mb-1">
                    {{ invite.team.name }}
                  </h6>
                  <p class="text-body-2 mb-0">
                    {{ currentTab === 'sent' ? `To: ${invite.inviteeEmail}` : `From: ${invite.invitedBy.username}` }}
                  </p>
                </div>
              </div>
              
              <div class="d-flex align-center gap-2 mb-2">
                <VChip
                  :color="getStatusColor(invite.status)"
                  size="small"
                  variant="tonal"
                  class="text-capitalize"
                >
                  {{ invite.status }}
                </VChip>
                <VChip
                  size="small"
                  variant="outlined"
                  class="text-capitalize"
                >
                  {{ invite.role }}
                </VChip>
                <VChip
                  v-if="isExpired(invite) && invite.status === 'pending'"
                  color="error"
                  size="small"
                  variant="tonal"
                >
                  Expired
                </VChip>
              </div>

              <p
                v-if="invite.message"
                class="text-body-2 text-disabled mb-2"
              >
                "{{ invite.message }}"
              </p>

              <p class="text-caption text-disabled mb-0">
                Sent {{ formatDate(invite.createdAt) }} • Expires {{ formatDate(invite.expirationDate) }}
              </p>
            </VCol>

            <VCol
              cols="12"
              md="6"
              class="text-md-end"
            >
              <!-- Received Invites Actions -->
              <div v-if="currentTab === 'received' && invite.status === 'pending' && !isExpired(invite)">
                <VBtn
                  color="success"
                  variant="tonal"
                  size="small"
                  class="me-2"
                  @click="acceptInvite(invite)"
                >
                  <VIcon
                    start
                    icon="tabler-check"
                  />
                  Accept
                </VBtn>
                <VBtn
                  color="error"
                  variant="outlined"
                  size="small"
                  @click="rejectInvite(invite)"
                >
                  <VIcon
                    start
                    icon="tabler-x"
                  />
                  Reject
                </VBtn>
              </div>

              <!-- Sent Invites Actions -->
              <div v-else-if="currentTab === 'sent' && invite.status === 'pending'">
                <VBtn
                  v-if="isExpired(invite)"
                  color="primary"
                  variant="tonal"
                  size="small"
                  class="me-2"
                  @click="resendInvite(invite)"
                >
                  <VIcon
                    start
                    icon="tabler-refresh"
                  />
                  Resend
                </VBtn>
                <VBtn
                  color="error"
                  variant="outlined"
                  size="small"
                  @click="cancelInvite(invite)"
                >
                  <VIcon
                    start
                    icon="tabler-trash"
                  />
                  Cancel
                </VBtn>
              </div>

              <!-- Status Display -->
              <div
                v-else
                class="text-body-2 text-disabled"
              >
                {{ invite.status === 'accepted' ? 'Invitation accepted' :
                  invite.status === 'rejected' ? 'Invitation rejected' :
                  invite.status === 'expired' ? 'Invitation expired' : 'No actions available' }}
              </div>
            </VCol>
          </VRow>
        </VCardText>
      </VCard>
    </div>
  </VCardText>
</template>
