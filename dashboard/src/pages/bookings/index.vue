<script setup>
import FullCalendar from '@fullcalendar/vue3'
import dayGridPlugin from '@fullcalendar/daygrid'
import interactionPlugin from '@fullcalendar/interaction'
import listPlugin from '@fullcalendar/list'
import timeGridPlugin from '@fullcalendar/timegrid'
import { useSocketStore } from '@stores/auth'
import { useSocketBookingStore } from '@stores/bookings'
import { socket } from '@socket/socket'
import { toast } from 'vue3-toastify'
import 'vue3-toastify/dist/index.css'
import { useTheme } from 'vuetify'
import { useHead } from '@unhead/vue'

// Components
import BookingEventHandler from '@/views/bookings/BookingEventHandler.vue'

const storeAuth = useSocketStore()
const bookingStore = useSocketBookingStore()

const { user } = storeToRefs(storeAuth)
const { bookings, booking } = storeToRefs(bookingStore)

const vuetifyTheme = useTheme()

useHead({
  title: 'Qwote Z | Bookings Calendar',
  meta: [
    {
      name: 'description',
      content: 'Manage your bookings with calendar view',
    },
  ],
})

// Socket event listeners
socket.on('allBookings', data => {
  bookings.value = data.data
})

socket.on('createBooking', data => {
  switch (data.status) {
  case 'success':
    bookings.value = data.data.bookings
    booking.value = data.data.booking

    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })
    break
  case 'error':
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
    break
  default:
    break
  }
})

socket.on('updateBooking', data => {
  switch (data.status) {
  case 'success':
    bookings.value = data.data.bookings
    booking.value = data.data.booking

    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })
    break
  case 'error':
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
    break
  default:
    break
  }
})

socket.on('deleteBooking', data => {
  switch (data.status) {
  case 'success':
    bookings.value = data.data.bookings

    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })
    break
  case 'error':
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
    break
  default:
    break
  }
})

// Load bookings on mount
onMounted(() => {
  if (!bookings.value) {
    bookingStore.getAllBookings({
      user: user.value._id,
    })
  }
})

// Booking event handler
const currentBooking = ref({
  id: null,
  title: '',
  customerName: '',
  startTime: new Date(),
  endTime: new Date(),
  allDay: false,
  location: '',
  notes: '',
  status: 'pending',
  priority: 'medium',
  color: '#1976d2',
})

const isBookingHandlerSidebarActive = ref(false)

watch(isBookingHandlerSidebarActive, val => {
  if (!val) {
    currentBooking.value = {
      id: null,
      title: '',
      customerName: '',
      startTime: new Date(),
      endTime: new Date(),
      allDay: false,
      location: '',
      notes: '',
      status: 'pending',
      priority: 'medium',
      color: '#1976d2',
    }
  }
})

// Calendar setup
const refCalendar = ref()
const calendarApi = ref()

const calendarOptions = computed(() => ({
  plugins: [dayGridPlugin, interactionPlugin, timeGridPlugin, listPlugin],
  initialView: 'dayGridMonth',
  headerToolbar: {
    start: 'prev,next today',
    center: 'title',
    end: 'dayGridMonth,timeGridWeek,timeGridDay,listWeek',
  },
  events: bookings.value?.map(booking => ({
    id: booking._id,
    title: `${booking.title} - ${booking.customerName}`,
    start: booking.startTime,
    end: booking.endTime,
    allDay: booking.allDay,
    backgroundColor: booking.color || '#1976d2',
    borderColor: booking.color || '#1976d2',
    extendedProps: {
      ...booking,
    },
  })) || [],
  editable: true,
  selectable: true,
  selectMirror: true,
  dayMaxEvents: true,
  weekends: true,
  select: handleDateSelect,
  eventClick: handleEventClick,
  eventDrop: handleEventDrop,
  eventResize: handleEventResize,
}))

function handleDateSelect(selectInfo) {
  currentBooking.value = {
    id: null,
    title: '',
    customerName: '',
    startTime: selectInfo.start,
    endTime: selectInfo.end,
    allDay: selectInfo.allDay,
    location: '',
    notes: '',
    status: 'pending',
    priority: 'medium',
    color: '#1976d2',
  }
  isBookingHandlerSidebarActive.value = true
}

function handleEventClick(clickInfo) {
  const bookingData = clickInfo.event.extendedProps

  currentBooking.value = {
    id: bookingData._id,
    title: bookingData.title,
    customerName: bookingData.customerName,
    startTime: new Date(bookingData.startTime),
    endTime: new Date(bookingData.endTime),
    allDay: bookingData.allDay,
    location: bookingData.location || '',
    notes: bookingData.notes || '',
    status: bookingData.status || 'pending',
    priority: bookingData.priority || 'medium',
    color: bookingData.color || '#1976d2',
  }
  isBookingHandlerSidebarActive.value = true
}

function handleEventDrop(dropInfo) {
  updateBookingDates(dropInfo.event)
}

function handleEventResize(resizeInfo) {
  updateBookingDates(resizeInfo.event)
}

function updateBookingDates(event) {
  const bookingData = event.extendedProps

  const updatedBooking = {
    id: bookingData._id,
    title: bookingData.title,
    customerName: bookingData.customerName,
    startTime: event.start,
    endTime: event.end,
    allDay: event.allDay,
    location: bookingData.location,
    notes: bookingData.notes,
    status: bookingData.status,
    priority: bookingData.priority,
    color: bookingData.color,
  }

  bookingStore.updateBooking({
    user: user.value._id,
    booking: updatedBooking,
  })
}

function addBooking(bookingData) {
  bookingStore.createBooking({
    user: user.value._id,
    booking: bookingData,
  })
  isBookingHandlerSidebarActive.value = false
}

function updateBooking(bookingData) {
  bookingStore.updateBooking({
    user: user.value._id,
    booking: bookingData,
  })
  isBookingHandlerSidebarActive.value = false
}

function removeBooking(bookingId) {
  bookingStore.deleteBooking({
    user: user.value._id,
    id: bookingId,
  })
  isBookingHandlerSidebarActive.value = false
}

// Calendar API setup
onMounted(() => {
  nextTick(() => {
    if (refCalendar.value) {
      calendarApi.value = refCalendar.value.getApi()
    }
  })
})
</script>

<template>
  <div>
    <VCard>
      <VCardTitle class="d-flex align-center justify-space-between">
        <span>Bookings Calendar</span>
        <VBtn
          color="primary"
          prepend-icon="tabler-plus"
          @click="isBookingHandlerSidebarActive = true"
        >
          Add Booking
        </VBtn>
      </VCardTitle>

      <VCardText>
        <FullCalendar
          ref="refCalendar"
          :options="calendarOptions"
        />
      </VCardText>
    </VCard>

    <BookingEventHandler
      v-model:is-drawer-open="isBookingHandlerSidebarActive"
      :booking="currentBooking"
      @add-booking="addBooking"
      @update-booking="updateBooking"
      @remove-booking="removeBooking"
    />
  </div>
</template>

<style lang="scss">
@use "@core/scss/template/libs/full-calendar";
</style>
