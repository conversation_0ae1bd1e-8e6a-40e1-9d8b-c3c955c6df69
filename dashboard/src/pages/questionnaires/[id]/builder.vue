<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { storeToRefs } from 'pinia'
import { useSocketStore } from '@stores/auth'
import { useSocketQuestionnaireStore } from '@stores/questionnaires'
import { socket } from '@socket/socket'
import { toast } from 'vue3-toastify'
import 'vue3-toastify/dist/index.css'
import { useTheme } from 'vuetify'
import { useHead } from '@unhead/vue'

const route = useRoute()
const storeAuth = useSocketStore()
const questionnaireStore = useSocketQuestionnaireStore()

const { user } = storeToRefs(storeAuth)
const { questionnaire, questions } = storeToRefs(questionnaireStore)

const vuetifyTheme = useTheme()
const router = useRouter()

const questionnaireId = computed(() => route.params.id)

definePage({
  meta: {
    layout: 'default',
  },
})

useHead({
  title: 'Questionnaire Builder | Qwote Z',
  meta: [
    {
      name: 'description',
      content: 'Build and manage questionnaire questions',
    },
  ],
})

// State
const loading = ref(false)
const saving = ref(false)
const isAddQuestionDialogOpen = ref(false)
const editingQuestion = ref(null)

// Question form
const questionForm = ref({
  text: '',
  type: 'text',
  required: false,
  options: [],
  order: 0,
  placeholder: '',
})

// Question types (matching backend model enum values)
const questionTypes = [
  { value: 'text', title: 'Text Input' },
  { value: 'textarea', title: 'Long Text' },
  { value: 'select', title: 'Dropdown' },
  { value: 'radio', title: 'Radio Buttons' },
  { value: 'checkbox', title: 'Checkboxes' },
  { value: 'multiple-choice', title: 'Multiple Choice (Legacy)' },
  { value: 'number', title: 'Number' },
  { value: 'email', title: 'Email' },
  { value: 'date', title: 'Date' },
  { value: 'url', title: 'URL' },
  { value: 'tel', title: 'Phone Number' },
]

// Socket event listeners
socket.on('singleQuestionnaire', data => {
  loading.value = false
  if (data.status === 'success') {
    questionnaire.value = data.data
    loadQuestions()
  } else {
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
  }
})

socket.on('allQuestionnaireQuestions', data => {
  if (data.status === 'success') {
    questions.value = data.data
  } else {
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
  }
})

socket.on('createQuestionnaireQuestion', data => {
  saving.value = false
  if (data.status === 'success') {
    questions.value = data.data.questions
    isAddQuestionDialogOpen.value = false
    resetQuestionForm()
    toast(data.message, {
      autoClose: 3000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })
  } else {
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
  }
})

socket.on('updateQuestionnaireQuestion', data => {
  saving.value = false
  if (data.status === 'success') {
    questions.value = data.data.questions
    isAddQuestionDialogOpen.value = false
    editingQuestion.value = null
    resetQuestionForm()
    toast(data.message, {
      autoClose: 3000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })
  } else {
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
  }
})

socket.on('deleteQuestionnaireQuestion', data => {
  if (data.status === 'success') {
    questions.value = data.data.questions
    toast(data.message, {
      autoClose: 3000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })
  } else {
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
  }
})

// Load questionnaire and questions on mount
onMounted(() => {
  if (questionnaireId.value) {
    loading.value = true
    questionnaireStore.getQuestionnaire({
      user: user.value._id,
      id: questionnaireId.value,
    })
  }
})

// Methods
function loadQuestions() {
  questionnaireStore.getQuestions({
    user: user.value._id,
    questionnaireId: questionnaireId.value,
  })
}

function handleAddQuestion() {
  resetQuestionForm()
  questionForm.value.order = (questions.value?.length || 0) + 1
  isAddQuestionDialogOpen.value = true
}

function handleEditQuestion(question) {
  editingQuestion.value = question
  questionForm.value = {
    text: question.text,
    type: question.type,
    required: question.required || false,
    options: question.options || [],
    order: question.order || 0,
    placeholder: question.placeholder || '',
  }
  isAddQuestionDialogOpen.value = true
}

function handleDeleteQuestion(question) {
  if (confirm('Are you sure you want to delete this question?')) {
    questionnaireStore.deleteQuestion({
      user: user.value._id,
      id: question._id,
    })
  }
}

function handleSaveQuestion() {
  if (!questionForm.value.text.trim()) {
    toast('Question text is required', {
      autoClose: 3000,
      type: 'error',
    })

    return
  }

  saving.value = true

  const questionData = {
    ...questionForm.value,
    questionnaire: questionnaireId.value,
  }

  console.log('Saving question with data:', questionData)

  if (editingQuestion.value) {
    questionnaireStore.updateQuestion({
      user: user.value._id,
      question: {
        id: editingQuestion.value._id,
        ...questionData,
      },
    })
  } else {
    questionnaireStore.createQuestion({
      user: user.value._id,
      question: questionData,
    })
  }
}

function handleCancelQuestion() {
  isAddQuestionDialogOpen.value = false
  editingQuestion.value = null
  resetQuestionForm()
}

function resetQuestionForm() {
  questionForm.value = {
    text: '',
    type: 'text',
    required: false,
    options: [],
    order: 0,
    placeholder: '',
  }
}

function addOption() {
  questionForm.value.options.push('')
}

function removeOption(index) {
  questionForm.value.options.splice(index, 1)
}

function handleBack() {
  router.push({ name: 'questionnaires' })
}

// Computed
const isFormValid = computed(() => {
  return questionForm.value.text.trim() !== ''
})

const needsOptions = computed(() => {
  return ['select', 'radio', 'checkbox', 'multiple-choice'].includes(questionForm.value.type)
})

const sortedQuestions = computed(() => {
  if (!questions.value) return []
  
  return [...questions.value].sort((a, b) => (a.order || 0) - (b.order || 0))
})
</script>

<template>
  <div>
    <!-- Header -->
    <div class="d-flex align-center justify-space-between mb-6">
      <div>
        <h1 class="text-h4 font-weight-bold mb-2">
          Questionnaire Builder
        </h1>
        <p class="text-body-1 text-medium-emphasis">
          {{ questionnaire?.title || 'Loading...' }}
        </p>
      </div>
      <div class="d-flex gap-2">
        <VBtn
          color="secondary"
          variant="outlined"
          prepend-icon="tabler-arrow-left"
          @click="handleBack"
        >
          Back to List
        </VBtn>
        <VBtn
          color="primary"
          prepend-icon="tabler-plus"
          @click="handleAddQuestion"
        >
          Add Question
        </VBtn>
      </div>
    </div>

    <!-- Loading State -->
    <div
      v-if="loading"
      class="text-center py-12"
    >
      <VProgressCircular
        indeterminate
        color="primary"
        size="48"
      />
      <div class="mt-4 text-body-1">
        Loading questionnaire...
      </div>
    </div>

    <!-- Questions List -->
    <div v-else>
      <div v-if="sortedQuestions.length > 0">
        <VCard
          v-for="(question, index) in sortedQuestions"
          :key="question._id"
          class="mb-4"
        >
          <VCardTitle class="d-flex align-center justify-space-between">
            <div class="d-flex align-center">
              <VChip
                size="small"
                color="primary"
                variant="tonal"
                class="me-3"
              >
                {{ index + 1 }}
              </VChip>
              <span>{{ question.text }}</span>
              <VChip
                v-if="question.required"
                size="x-small"
                color="error"
                variant="tonal"
                class="ms-2"
              >
                Required
              </VChip>
            </div>
            
            <div class="d-flex gap-1">
              <VBtn
                icon
                size="small"
                variant="text"
                @click="handleEditQuestion(question)"
              >
                <VIcon icon="tabler-edit" />
              </VBtn>
              <VBtn
                icon
                size="small"
                variant="text"
                color="error"
                @click="handleDeleteQuestion(question)"
              >
                <VIcon icon="tabler-trash" />
              </VBtn>
            </div>
          </VCardTitle>
          
          <VCardText>
            <div class="d-flex align-center gap-4">
              <VChip
                size="small"
                variant="outlined"
              >
                {{ questionTypes.find(t => t.value === question.type)?.title || question.type }}
              </VChip>
              
              <div
                v-if="question.options && question.options.length > 0"
                class="d-flex gap-2"
              >
                <span class="text-caption text-medium-emphasis">Options:</span>
                <VChip
                  v-for="option in question.options"
                  :key="option"
                  size="x-small"
                  variant="tonal"
                >
                  {{ option }}
                </VChip>
              </div>
            </div>
          </VCardText>
        </VCard>
      </div>
      
      <!-- Empty State -->
      <div
        v-else
        class="text-center py-12"
      >
        <VIcon
          icon="tabler-help"
          size="64"
          class="text-medium-emphasis mb-4"
        />
        <h3 class="text-h6 mb-2">
          No Questions Yet
        </h3>
        <p class="text-body-2 text-medium-emphasis mb-4">
          Start building your questionnaire by adding questions
        </p>
        <VBtn
          color="primary"
          @click="handleAddQuestion"
        >
          Add Your First Question
        </VBtn>
      </div>
    </div>

    <!-- Add/Edit Question Dialog -->
    <VDialog
      v-model="isAddQuestionDialogOpen"
      max-width="600"
      persistent
    >
      <VCard>
        <VCardTitle>
          {{ editingQuestion ? 'Edit Question' : 'Add Question' }}
        </VCardTitle>

        <VCardText>
          <VForm @submit.prevent="handleSaveQuestion">
            <VRow>
              <VCol cols="12">
                <AppTextField
                  v-model="questionForm.text"
                  label="Question Text"
                  placeholder="Enter your question"
                  :rules="[v => !!v || 'Question text is required']"
                />
              </VCol>

              <VCol
                cols="12"
                md="6"
              >
                <VSelect
                  v-model="questionForm.type"
                  label="Question Type"
                  :items="questionTypes"
                  item-title="title"
                  item-value="value"
                />
              </VCol>

              <VCol
                cols="12"
                md="6"
              >
                <VCheckbox
                  v-model="questionForm.required"
                  label="Required question"
                  density="compact"
                />
              </VCol>

              <VCol cols="12">
                <AppTextField
                  v-model="questionForm.placeholder"
                  label="Placeholder Text (Optional)"
                  placeholder="Enter placeholder text for the input field"
                />
              </VCol>

              <!-- Options for select/radio/checkbox -->
              <VCol
                v-if="needsOptions"
                cols="12"
              >
                <div class="mb-3">
                  <div class="d-flex align-center justify-space-between mb-2">
                    <VLabel>Options</VLabel>
                    <VBtn
                      size="small"
                      color="primary"
                      variant="outlined"
                      @click="addOption"
                    >
                      Add Option
                    </VBtn>
                  </div>
                  
                  <div
                    v-for="(option, index) in questionForm.options"
                    :key="index"
                    class="d-flex align-center gap-2 mb-2"
                  >
                    <AppTextField
                      v-model="questionForm.options[index]"
                      :label="`Option ${index + 1}`"
                      density="compact"
                    />
                    <VBtn
                      icon
                      size="small"
                      color="error"
                      variant="text"
                      @click="removeOption(index)"
                    >
                      <VIcon icon="tabler-x" />
                    </VBtn>
                  </div>
                </div>
              </VCol>
            </VRow>
          </VForm>
        </VCardText>

        <VCardActions>
          <VSpacer />
          <VBtn
            color="secondary"
            variant="outlined"
            @click="handleCancelQuestion"
          >
            Cancel
          </VBtn>
          <VBtn
            color="primary"
            :loading="saving"
            :disabled="!isFormValid"
            @click="handleSaveQuestion"
          >
            {{ editingQuestion ? 'Update' : 'Add' }} Question
          </VBtn>
        </VCardActions>
      </VCard>
    </VDialog>
  </div>
</template>
