<script setup>
import { useSocketStore } from '@stores/auth'
import { useNotificationsSocketStore } from '@stores/notifications'
import { socket } from '@socket/socket'
import { storeToRefs } from 'pinia'
import pdf from '@images/icons/project-icons/pdf.png'
import { useHead } from '@unhead/vue'
import { toast } from 'vue3-toastify'
import 'vue3-toastify/dist/index.css'
import { useTheme } from 'vuetify'
import moment from 'moment'

useHead({
  title: 'Qwote Z | Notifications',
  meta: [
    {
      name: 'description',
      content: 'My page description',
    },
  ],
})

const store = useSocketStore()
const storeNotification = useNotificationsSocketStore()

const { user } = storeToRefs(store)
const { appNotifications, notifications } = storeToRefs(storeNotification)

const vuetifyTheme = useTheme()

socket.on('allAppNotifications', data => {
  appNotifications.value = data.data
})

socket.on('allNotifications', data => {
  notifications.value = data.data
})

socket.on('markAsRead', data => {
  notifications.value = data.data
})

socket.on('markAsUnread', data => {
  notifications.value = data.data
})

moment.updateLocale('en', {
  relativeTime: {
    future: 'in %s',
    past: '%s ago',
    s: 'a few seconds',
    ss: '%d seconds',
    m: 'a minute',
    mm: '%d minutes',
    h: '1 hour ago', //this is the setting that you need to change
    hh: '%d hours',
    d: 'a day',
    dd: '%d days',
    w: 'a week',
    ww: '%d weeks',
    M: '1 month ago', //change this for month
    MM: '%d months',
    y: 'a year',
    yy: '%d years',
  },
})

const markRead = notificationId => {
  notifications.value.forEach(item => {
    notificationId.forEach(id => {
      if (id === item.id)
        item.isRead = true
    })
  })

  storeNotification.markNotificationAsRead({
    user: user.value._id,
    id: notificationId[0],
  })
}

const markUnRead = notificationId => {
  notifications.value.forEach(item => {
    notificationId.forEach(id => {
      if (id === item.id)
        item.isRead = false
    })
  })

  storeNotification.markNotificationAsUnread({
    user: user.value._id,
    id: notificationId[0],
  })
}

const handleNotificationClick = notification => {
  if (!notification.isRead)
    markRead([notification.id])
}

const sortedNotifications = computed(() => {
  // eslint-disable-next-line vue/no-side-effects-in-computed-properties
  return notifications.value.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
})
</script>

<template>
  <VCard title="All Notifications">
    <VCardText>
      <VTimeline
        side="end"
        align="start"
        line-inset="8"
        truncate-line="start"
        density="compact"
      >
        <!-- SECTION Timeline Item: Flight -->
        <VTimelineItem
          v-for="notification in sortedNotifications"
          :key="notification._id"
          :dot-color="notification.type"
          size="x-small"
        >
          <!-- 👉 Header -->
          <div class="d-flex justify-space-between align-center gap-2 flex-wrap mb-2">
            <span class="app-timeline-title">
              {{ notification.title }}
            </span>
            <span class="app-timeline-meta">{{ moment(notification.createdAt).fromNow() }}</span>
          </div>

          <!-- 👉 Content -->
          <div class="app-timeline-text mt-1">
            {{ notification.description }}
          </div>
        </VTimelineItem>
        <!-- !SECTION -->
      </VTimeline>
    </VCardText>
  </VCard>
</template>
