<script setup>

//TODO: fix menu items when in different acl
import { useSocketStore } from '@stores/auth'
import AuthProvider from '@/views/pages/authentication/AuthProvider.vue'
import authV1BottomShape from '@images/svg/auth-v1-bottom-shape.svg?raw'
import authV1TopShape from '@images/svg/auth-v1-top-shape.svg?raw'
import { VNodeRenderer } from '@layouts/components/VNodeRenderer'
import { themeConfig } from '@themeConfig'
import { socket } from '@socket/socket'
import { useHead } from '@unhead/vue'
import { toast } from 'vue3-toastify'
import 'vue3-toastify/dist/index.css'
import { useAbility } from '@/plugins/casl/composables/useAbility'
import { useTheme } from 'vuetify'

definePage({
  meta: {
    layout: 'blank',
    public: true,
    unauthenticatedOnly: true,
    redirectIfLoggedIn: true,
  },
})

useHead({
  title: 'Qwote Z | Login',
  meta: [
    {
      name: 'description',
      content: 'My page description',
    },
  ],
})

const store = useSocketStore()

const { token, user, me } = storeToRefs(store)

const ability = useAbility()
const router = useRouter()
const vuetifyTheme = useTheme()

socket.on('login', data => {
  switch (data.status) {
  case 'success':
    localStorage.setItem('authenticated', true)

    const accessToken = data.token
    const userData = data.user
    const userAbilities = data.user.ability

    token.value = data.token
    user.value = data.user

    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })

    if (user.value.isMfaActive) {
      socket.disconnect()
      socket.auth = { token: token, userId: user.value._id }
      socket.connect()
      isTwoFactor.value = true

      const userAbilities = [{
        'action': 'verify',
        'subject': '2FA',
      }]

      localStorage.setItem('userAbilities', JSON.stringify(userAbilities))
      ability.update(userAbilities)
    } else if (user.value.isOtpActive) {
      socket.disconnect()
      socket.auth = { token: token, userId: user.value._id }
      socket.connect()

      store.setupOtp({
        id: user.value._id,
        email: user.value.email,
      })

      isOtp.value = true

      const userAbilities = [{
        'action': 'verify',
        'subject': 'OTP',
      }]

      localStorage.setItem('userAbilities', JSON.stringify(userAbilities))
      ability.update(userAbilities)
    } else if (user.value.newUser === true) {
      localStorage.setItem('userAbilities', JSON.stringify(userAbilities))
      ability.update(userAbilities)

      sessionStorage.setItem('userData', JSON.stringify(userData))
      sessionStorage.setItem('accessToken', JSON.stringify(accessToken))
      sessionStorage.setItem('userRole', JSON.stringify({ role: user.value.role }))

      localStorage.setItem('userData', JSON.stringify(userData))
      localStorage.setItem('accessToken', JSON.stringify(accessToken))

      setupIdentitiesDialog.value = true
    } else {
      localStorage.setItem('userAbilities', JSON.stringify(userAbilities))
      ability.update(userAbilities)

      sessionStorage.setItem('userData', JSON.stringify(userData))
      sessionStorage.setItem('accessToken', JSON.stringify(accessToken))
      sessionStorage.setItem('userRole', JSON.stringify({ role: user.value.role }))

      localStorage.setItem('userData', JSON.stringify(userData))
      localStorage.setItem('accessToken', JSON.stringify(accessToken))
      
      setTimeout(() => {
        router.go('/')
      }, 2000)
    }
    break
  case 'error':
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
    break
  default:
    break
  }
})

socket.on('createVerify2fa', data => {
  switch (data.status) {
  case 'success':
    token.value = data.data.token
    user.value = data.data.user

    const userAbilities = user.value.ability

    localStorage.setItem('userData', JSON.stringify(user.value))
    localStorage.setItem('accessToken', JSON.stringify(token.value))

    sessionStorage.setItem('userData', JSON.stringify(user.value))
    sessionStorage.setItem('accessToken', JSON.stringify(token.value))
    sessionStorage.setItem('userRole', JSON.stringify({ role: user.value.role }))

    localStorage.setItem('userAbilities', JSON.stringify(userAbilities))
    ability.update(userAbilities)

    isTwoFactor.value = false

    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })


    if (user.value.newUser === true) {
      setupIdentitiesDialog.value = true
    } else {
      setTimeout(() => {
        router.replace({ name: 'root' })
      }, 2000)
    }

    break
  case 'error':
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
    break
  default:
    break
  }
})

socket.on('createTwoFactor', data => {
  switch (data.status) {
  case 'success':
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })
    break
  case 'error':
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
    break
  default:
    break
  }
})

socket.on('verifyTwoFactor', data => {
  switch (data.status) {
  case 'success':

    token.value = data.data.token
    user.value = data.data.user

    const userAbilities = user.value.ability

    localStorage.setItem('userData', JSON.stringify(user.value))
    localStorage.setItem('accessToken', JSON.stringify(token.value))

    sessionStorage.setItem('userData', JSON.stringify(user.value))
    sessionStorage.setItem('accessToken', JSON.stringify(token.value))
    sessionStorage.setItem('userRole', JSON.stringify({ role: user.value.role }))

    localStorage.setItem('userAbilities', JSON.stringify(userAbilities))
    ability.update(userAbilities)

    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })

    isOtp.value = false

    if (user.value.newUser === true) {
      setupIdentitiesDialog.value = true
    } else {
      setTimeout(() => {
        router.replace({ name: 'root' })
      }, 2000)
    }
    break
  case 'error':
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
    break
  default:
    break
  }
})

const form = ref({
  email: '',
  password: '',
  remember: false,
})

const setupIdentitiesDialog = ref(false)
const isTwoFactor = ref(false)
const isOtp = ref(false)
const FACode = ref('')
const OTPCode = ref('')

const login = () => {
  store.login(form.value)
}

const verify2Fa = () => {
  store.verify2Fa({
    id: user.value._id,
    token: FACode.value,
  })
}

const verifyOtp = () => {
  store.verifyOtp({
    id: user.value._id,
    email: user.value.email,
    otp: OTPCode.value,
  })
}

const isPasswordVisible = ref(false)

const gotoDashboard = () => {
  setupIdentitiesDialog.value = false
  router.go()
}

const setupIdentities = () => {
  setupIdentitiesDialog.value = false
  router.push({ name: 'onboarding' })
}
</script>

<template>
  <div class="auth-wrapper d-flex align-center justify-center pa-4">
    <div class="position-relative my-sm-16">
      <!-- 👉 Top shape -->
      <VNodeRenderer
        :nodes="h('div', { innerHTML: authV1TopShape })"
        class="text-primary auth-v1-top-shape d-none d-sm-block"
      />

      <!-- 👉 Bottom shape -->
      <VNodeRenderer
        :nodes="h('div', { innerHTML: authV1BottomShape })"
        class="text-primary auth-v1-bottom-shape d-none d-sm-block"
      />

      <!-- 👉 Auth Card -->
      <VCard
        class="auth-card"
        max-width="460"
        :class="$vuetify.display.smAndUp ? 'pa-6' : 'pa-0'"
      >
        <VCardItem class="justify-center">
          <VCardTitle>
            <RouterLink to="/">
              <div class="app-logo">
                <VNodeRenderer :nodes="themeConfig.app.logo" />
                <h1 class="app-logo-title">
                  {{ themeConfig.app.title }}
                </h1>
              </div>
            </RouterLink>
          </VCardTitle>
        </VCardItem>

        <VCardText>
          <h4 class="text-h4 mb-1">
            Welcome to <span class="text-capitalize">{{ themeConfig.app.title }}</span>! 👋🏻
          </h4>
          <p class="mb-0">
            Please sign-in to your account and start the adventure
          </p>
        </VCardText>

        <VCardText>
          <VForm @submit.prevent="login">
            <VRow>
              <!-- email -->
              <VCol cols="12">
                <VTextField
                  v-model="form.email"
                  autofocus
                  label="Email"
                  type="email"
                  placeholder="<EMAIL>"
                />
              </VCol>

              <!-- password -->
              <VCol cols="12">
                <VTextField
                  v-model="form.password"
                  label="Password"
                  placeholder="············"
                  :type="isPasswordVisible ? 'text' : 'password'"
                  :append-inner-icon="isPasswordVisible ? 'ri-eye-off-line' : 'ri-eye-line'"
                  @click:append-inner="isPasswordVisible = !isPasswordVisible"
                />

                <!-- remember me checkbox -->
                <div class="d-flex align-center justify-space-between flex-wrap my-6">
                  <VCheckbox
                    v-model="form.remember"
                    label="Remember me"
                  />

                  <RouterLink
                    class="text-primary"
                    :to="{ name: 'auth-forgot-password' }"
                  >
                    Forgot Password?
                  </RouterLink>
                </div>

                <!-- login button -->
                <VBtn
                  block
                  type="submit"
                >
                  Login
                </VBtn>
              </VCol>

              <!-- create account -->
              <VCol
                cols="12"
                class="text-body-1 text-center"
              >
                <span class="d-inline-block">
                  New on our platform?
                </span>
                <RouterLink
                  class="text-primary ms-2 d-inline-block text-body-1"
                  :to="{ name: 'auth-register' }"
                >
                  Create an account
                </RouterLink>
              </VCol>

              <VCol
                cols="12"
                class="d-flex align-center"
              >
                <VDivider />
                <span class="mx-4 text-high-emphasis">or</span>
                <VDivider />
              </VCol>

              <!-- auth providers -->
              <VCol
                cols="12"
                class="text-center"
              >
                <AuthProvider />
              </VCol>
            </VRow>
          </VForm>
        </VCardText>
      </VCard>
    </div>

    <VDialog
      v-model="isTwoFactor"
      persistent
      class="v-dialog-sm"
    >
      <!-- Dialog Content -->
      <VCard class="pa-sm-11 pa-3">
        <VCardText>
          <h4 class="text-h4 mb-1">
            2FA Authenticator Verification 💬
          </h4>
          <p class="mb-1">
            We sent a verification code to your mobile Authenticator App. Enter the code from the mobile in the field
            below.
          </p>
        </VCardText>

        <VCardText>
          <VForm @submit.prevent="verify2Fa">
            <!-- email -->
            <h6 class="text-body-1">
              Type your 6 digit security code
            </h6>
            <VOtpInput
              v-model="FACode"
              type="number"
              autofocus
              class="pa-0"
              @finish="verify2Fa"
            />

            <!-- reset password -->
            <VBtn
              block
              type="submit"
              class="mt-3 mb-5"
            >
              Verify my account
            </VBtn>
          </VForm>
        </VCardText>
      </VCard>
    </VDialog>

    <VDialog
      v-model="isOtp"
      persistent
      class="v-dialog-sm"
    >
      <!-- Dialog Content -->
      <VCard class="pa-sm-11 pa-3">
        <VCardText>
          <h4 class="text-h4 mb-1">
            OTP Verification Code 💬
          </h4>
          <p class="mb-1">
            We sent a verification code to your email. Enter the code from the email in the field below.
          </p>
        </VCardText>

        <VCardText>
          <VForm @submit.prevent="verifyOtp">
            <!-- email -->
            <h6 class="text-body-1">
              Type your 6 digit security code
            </h6>
            <VOtpInput
              v-model="OTPCode"
              type="number"
              autofocus
              class="pa-0"
              @finish="verifyOtp"
            />

            <!-- reset password -->
            <VBtn
              block
              type="submit"
              class="mt-3 mb-5"
            >
              Verify my account
            </VBtn>
          </VForm>
        </VCardText>
      </VCard>
    </VDialog>
    <VDialog
      v-model="setupIdentitiesDialog"
      persistent
      class="v-dialog-sm"
    >
      <!-- Dialog close btn -->
      <DialogCloseBtn @click="setupIdentitiesDialog = !setupIdentitiesDialog" />

      <!-- Dialog Content -->
      <VCard title="Get Started!">
        <VCardText>
          Would you like to setup your <span class="text-primary">identities</span> now?
        </VCardText>

        <VCardText class="d-flex justify-end gap-3 flex-wrap">
          <VBtn
            color="secondary"
            variant="tonal"
            @click="gotoDashboard"
          >
            Not Now
          </VBtn>
          <VBtn @click="setupIdentities">
            Let's Go!
          </VBtn>
        </VCardText>
      </VCard>
    </VDialog>
  </div>
</template>

<style lang="scss">
@use "@core/scss/template/pages/page-auth";
</style>
