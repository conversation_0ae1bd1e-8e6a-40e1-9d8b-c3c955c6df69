<script setup>
import { computed, defineProps, defineEmits } from 'vue'

const props = defineProps({
  radioContent: {
    type: Array,
    required: true,
  },
  selectedRadio: {
    type: Object,
    default: null,
  },
  gridColumn: {
    type: Object,
    default: () => ({ sm: '6', cols: '12' }),
  },
})

const emit = defineEmits(['update:selectedRadio'])

// Internal model for v-radio-group
const internalSelected = computed({
  get: () => props.selectedRadio,
  set: val => emit('update:selectedRadio', val),
})
</script>

<template>
  <VRadioGroup v-model="internalSelected">
    <VRow>
      <VCol
        v-for="(item, index) in radioContent"
        :key="index"
        v-bind="gridColumn"
      >
        <VRadio
          :value="item"
          class="w-100"
        >
          <template #label>
            <slot :item="item" />
          </template>
        </VRadio>
      </VCol>
    </VRow>
  </VRadioGroup>
</template>
