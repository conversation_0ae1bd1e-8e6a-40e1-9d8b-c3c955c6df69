<script setup>
import { useSocketStore } from '@stores/auth'
import { socket } from '@socket/socket'
import authV1BottomShape from '@images/svg/auth-v1-bottom-shape.svg?raw'
import authV1TopShape from '@images/svg/auth-v1-top-shape.svg?raw'
import { VNodeRenderer } from '@layouts/components/VNodeRenderer'
import { themeConfig } from '@themeConfig'
import { useHead } from '@unhead/vue'

definePage({
  meta: {
    layout: 'blank',
    public: true,
    unauthenticatedOnly: true,
  },
})

useHead({
  title: 'Digital Tribe | Account Verified',
  meta: [
    {
      name: 'description',
      content: 'My page description',
    },
  ],
})

const store = useSocketStore()

const route = useRoute()
const router = useRouter()

socket.on('verifyAccount', data => {
  console.log({ data })
  if (data.user.verifyEmailToken) {
    verified.value = 'Verified'

    setTimeout(() => {
      router.replace({ name: 'auth-login' })
    }, 2000)
  } else {
    verified.value = 'Not Verified'
  }
})

onMounted(() => {
  store.verifyAccount({
    id: route.params.id,
    token: route.params.token,
  })
})

const verified = ref('')

const resendVerifyToken = () => {
  console.log('resend')
  store.resendVerifyAccount({
    id: route.params.id,
  })
}
</script>

<template>
  <div
    v-if="verified === 'Verified'"
    class="auth-wrapper d-flex align-center justify-center pa-4"
  >
    <div class="position-relative my-sm-16">
      <!-- 👉 Top shape -->
      <VNodeRenderer
        :nodes="h('div', { innerHTML: authV1TopShape })"
        class="text-primary auth-v1-top-shape d-none d-sm-block"
      />

      <!-- 👉 Bottom shape -->
      <VNodeRenderer
        :nodes="h('div', { innerHTML: authV1BottomShape })"
        class="text-primary auth-v1-bottom-shape d-none d-sm-block"
      />
      <!-- 👉 Auth card -->
      <VCard
        class="auth-card pa-4"
        max-width="448"
      >
        <VCardItem class="justify-center">
          <template #prepend>
            <div class="d-flex">
              <VNodeRenderer :nodes="themeConfig.app.logo" />
            </div>
          </template>

          <VCardTitle class="font-weight-bold text-capitalize text-h3 py-1 text-primary">
            {{ themeConfig.app.title }}
          </VCardTitle>
        </VCardItem>

        <VCardText class="pt-2 text-center">
          <h2>
            Your Account Has Been Verified!
          </h2>
          <p>We are now redirecting you to the login page</p>
          <VProgressLinear
            indeterminate
            color="primary"
          />
        </VCardText>
      </VCard>
    </div>
  </div>
  <div
    v-else
    class="auth-wrapper d-flex align-center justify-center pa-4"
  >
    <div class="position-relative my-sm-16">
      <!-- 👉 Top shape -->
      <VNodeRenderer
        :nodes="h('div', { innerHTML: authV1TopShape })"
        class="text-primary auth-v1-top-shape d-none d-sm-block"
      />

      <!-- 👉 Bottom shape -->
      <VNodeRenderer
        :nodes="h('div', { innerHTML: authV1BottomShape })"
        class="text-primary auth-v1-bottom-shape d-none d-sm-block"
      />
      <!-- 👉 Auth card -->
      <VCard
        class="auth-card pa-4"
        max-width="448"
      >
        <VCardItem class="justify-center">
          <template #prepend>
            <div class="d-flex">
              <VNodeRenderer :nodes="themeConfig.app.logo" />
            </div>
          </template>

          <VCardTitle class="font-weight-bold text-capitalize text-h3 py-1 text-primary">
            {{ themeConfig.app.title }}
          </VCardTitle>
        </VCardItem>

        <VCardText class="pt-2 text-center">
          <h4 class="text-h4">
            Invalid Verification Link!
          </h4>
          <p class="pt-2">
            Verification link is not valid! Please request a new link!
          </p>
          <VBtn @click="resendVerifyToken">
            Resend Verification Link
          </VBtn>
        </VCardText>
      </VCard>
    </div>
  </div>
</template>

<style lang="scss">
@use "@core/scss/template/pages/page-auth";
</style>
