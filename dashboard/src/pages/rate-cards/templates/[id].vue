<script setup>
import { ref, computed, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useSocketStore } from '@stores/auth'
import { useSocketRateCardTemplateStore } from '@stores/rate-cards/templates'
import { socket } from '@socket/socket'
import { toast } from 'vue3-toastify'
import 'vue3-toastify/dist/index.css'
import { useTheme } from 'vuetify'
import { useHead } from '@unhead/vue'
import { useRouter, useRoute } from 'vue-router'

const store = useSocketStore()
const templateStore = useSocketRateCardTemplateStore()
const { user } = storeToRefs(store)
const { templates } = storeToRefs(templateStore)

const vuetifyTheme = useTheme()
const router = useRouter()
const route = useRoute()

definePage({
  meta: {
    layout: 'default',
  },
})

// State
const loading = ref(true)
const template = ref(null)
const isDeleteDialogVisible = ref(false)

// Get template ID from route params
const templateId = computed(() => route.params.id)

// Set page title
useHead({
  title: computed(() => template.value ? `Qwote Z | ${template.value.templateName}` : 'Qwote Z | Rate Card Template'),
  meta: [
    {
      name: 'description',
      content: computed(() => template.value ? `View details for ${template.value.templateName}` : 'View rate card template details'),
    },
  ],
})

// Socket event listeners
socket.on('singleRateCardTemplate', data => {
  loading.value = false
  if (data.status === 'success') {
    template.value = data.data
  } else {
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
  }
})

socket.on('deleteRateCardTemplate', data => {
  if (data.status === 'success') {
    // Close the dialog first
    isDeleteDialogVisible.value = false

    // Show success message
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })

    // Navigate back to rate cards list
    router.push('/rate-cards')
  } else {
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
  }
})

// Load template on mount
onMounted(() => {
  if (templateId.value) {
    templateStore.getRateCardTemplate({
      user: user.value._id,
      id: templateId.value,
    })
  }
})

// Methods
function handleEdit() {
  router.push(`/rate-cards/templates/edit/${templateId.value}`)
}

function handleDelete() {
  isDeleteDialogVisible.value = true
}

function confirmDelete() {
  if (!template.value) return

  templateStore.deleteRateCardTemplate({
    user: user.value._id,
    id: template.value._id,
    identity: template.value.identity?._id || user.value.identity,
  })
}

function formatDate(date) {
  return new Date(date).toLocaleDateString()
}

function getTypeColor(type) {
  const colors = {
    Photography: 'primary',
    Video: 'success',
    Event: 'info',
    Commercial: 'warning',
    Wedding: 'pink',
    Portrait: 'purple',
    Product: 'orange',
    Custom: 'grey',
  }

  return colors[type] || 'grey'
}

function getTypeIcon(type) {
  const icons = {
    Photography: 'tabler-camera',
    Video: 'tabler-video',
    Event: 'tabler-calendar-event',
    Commercial: 'tabler-building',
    Wedding: 'tabler-heart',
    Portrait: 'tabler-user',
    Product: 'tabler-package',
    Custom: 'tabler-settings',
  }

  return icons[type] || 'tabler-template'
}

function formatCurrency(amount, currency = 'USD') {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
  }).format(amount || 0)
}

function getServiceCount(services) {
  return Array.isArray(services) ? services.length : 0
}
</script>

<template>
  <div>
    <!-- Header -->
    <div class="d-flex align-center justify-space-between mb-6">
      <div class="d-flex align-center gap-3">
        <VBtn
          color="secondary"
          variant="outlined"
          prepend-icon="tabler-arrow-left"
          @click="router.push('/rate-cards')"
        >
          Back to Rate Cards
        </VBtn>
      </div>
      <div class="d-flex gap-2">
        <VBtn
          color="primary"
          variant="outlined"
          prepend-icon="tabler-edit"
          @click="handleEdit"
        >
          Edit Template
        </VBtn>
        <VBtn
          color="error"
          variant="outlined"
          prepend-icon="tabler-trash"
          @click="handleDelete"
        >
          Delete
        </VBtn>
      </div>
    </div>

    <!-- Loading State -->
    <div
      v-if="loading"
      class="text-center py-12"
    >
      <VProgressCircular
        indeterminate
        color="primary"
        size="48"
      />
      <div class="mt-4 text-body-1">
        Loading template...
      </div>
    </div>

    <!-- Template Details -->
    <div v-else-if="template">
      <!-- Template Header -->
      <VCard class="mb-6">
        <VCardText>
          <div class="d-flex align-center justify-space-between mb-4">
            <div class="d-flex align-center gap-3">
              <VAvatar
                :color="getTypeColor(template.type)"
                size="56"
              >
                <VIcon
                  :icon="getTypeIcon(template.type)"
                  size="32"
                  color="white"
                />
              </VAvatar>
              <div>
                <h1 class="text-h4 font-weight-bold mb-1">
                  {{ template.templateName }}
                </h1>
                <div class="d-flex align-center gap-2">
                  <VChip
                    :color="getTypeColor(template.type)"
                    size="small"
                    variant="tonal"
                  >
                    {{ template.type }}
                  </VChip>
                  <VChip
                    color="primary"
                    size="small"
                    variant="tonal"
                  >
                    Template
                  </VChip>
                  <VChip
                    v-if="template.isDefault"
                    color="success"
                    size="small"
                    variant="tonal"
                  >
                    Default
                  </VChip>
                  <VChip
                    v-if="!template.isActive"
                    color="error"
                    size="small"
                    variant="tonal"
                  >
                    Inactive
                  </VChip>
                </div>
              </div>
            </div>
            <div
              v-if="template.usageCount !== undefined"
              class="text-right"
            >
              <div class="text-h5 font-weight-bold">
                {{ template.usageCount }}
              </div>
              <div class="text-caption text-medium-emphasis">
                Times Used
              </div>
            </div>
          </div>

          <VDivider class="mb-4" />

          <!-- Description -->
          <div class="mb-4">
            <div class="text-subtitle-1 font-weight-bold mb-2">
              Description
            </div>
            <p class="text-body-1">
              {{ template.description || 'No description provided' }}
            </p>
          </div>

          <!-- Metadata -->
          <div class="d-flex flex-wrap gap-y-4 gap-x-6 mb-4">
            <div>
              <div class="text-caption text-medium-emphasis mb-1">
                Created
              </div>
              <div class="d-flex align-center gap-2">
                <VIcon
                  icon="tabler-calendar"
                  size="16"
                  class="text-medium-emphasis"
                />
                <span>{{ formatDate(template.createdAt) }}</span>
              </div>
            </div>
            <div>
              <div class="text-caption text-medium-emphasis mb-1">
                Last Updated
              </div>
              <div class="d-flex align-center gap-2">
                <VIcon
                  icon="tabler-clock"
                  size="16"
                  class="text-medium-emphasis"
                />
                <span>{{ formatDate(template.updatedAt) }}</span>
              </div>
            </div>
            <div v-if="template.identity">
              <div class="text-caption text-medium-emphasis mb-1">
                Identity
              </div>
              <div class="d-flex align-center gap-2">
                <VIcon
                  icon="tabler-user"
                  size="16"
                  class="text-medium-emphasis"
                />
                <span>{{ template.identity.name }}</span>
              </div>
            </div>
            <div>
              <div class="text-caption text-medium-emphasis mb-1">
                Default Currency
              </div>
              <div class="d-flex align-center gap-2">
                <VIcon
                  icon="tabler-currency"
                  size="16"
                  class="text-medium-emphasis"
                />
                <span>{{ template.defaultCurrency || 'USD' }}</span>
              </div>
            </div>
            <div v-if="template.version">
              <div class="text-caption text-medium-emphasis mb-1">
                Version
              </div>
              <div class="d-flex align-center gap-2">
                <VIcon
                  icon="tabler-versions"
                  size="16"
                  class="text-medium-emphasis"
                />
                <span>{{ template.version }}</span>
              </div>
            </div>
          </div>

          <!-- Tags -->
          <div
            v-if="template.tags && template.tags.length > 0"
            class="mb-4"
          >
            <div class="text-subtitle-1 font-weight-bold mb-2">
              Tags
            </div>
            <div class="d-flex flex-wrap gap-2">
              <VChip
                v-for="tag in template.tags"
                :key="tag"
                size="small"
                variant="outlined"
              >
                {{ tag }}
              </VChip>
            </div>
          </div>
        </VCardText>
      </VCard>

      <!-- Services Section -->
      <div class="mb-6">
        <div class="d-flex align-center justify-space-between mb-4">
          <h2 class="text-h5 font-weight-bold">
            Services
          </h2>
          <div class="text-subtitle-1 text-medium-emphasis">
            {{ getServiceCount(template.services) }} services
          </div>
        </div>

        <!-- No Services -->
        <div
          v-if="!template.services || template.services.length === 0"
          class="text-center py-8"
        >
          <VIcon
            icon="tabler-list"
            size="48"
            class="text-medium-emphasis mb-2"
          />
          <div class="text-h6 mb-2">
            No Services
          </div>
          <p class="text-body-2 text-medium-emphasis">
            This template doesn't have any services defined yet.
          </p>
        </div>

        <!-- Services List -->
        <div v-else>
          <VCard
            v-for="(service, index) in template.services"
            :key="index"
            class="mb-3"
            variant="outlined"
          >
            <VCardText>
              <div class="d-flex align-center justify-space-between mb-2">
                <div class="text-h6 font-weight-bold d-flex align-center gap-2">
                  {{ service.serviceName }}
                  <VChip
                    v-if="service.isRequired"
                    color="warning"
                    size="x-small"
                    variant="tonal"
                  >
                    Required
                  </VChip>
                </div>
                <div class="text-h6 font-weight-bold">
                  {{ formatCurrency(service.defaultRate, template.defaultCurrency) }}
                </div>
              </div>
              <div class="d-flex align-center justify-space-between mb-2">
                <div class="d-flex align-center gap-2">
                  <VChip
                    v-if="service.category"
                    size="x-small"
                    variant="tonal"
                  >
                    {{ service.category }}
                  </VChip>
                  <VChip
                    size="x-small"
                    variant="outlined"
                  >
                    {{ service.unit }}
                  </VChip>
                </div>
              </div>
              <p
                v-if="service.description"
                class="text-body-2 text-medium-emphasis"
              >
                {{ service.description }}
              </p>
            </VCardText>
          </VCard>
        </div>
      </div>
    </div>

    <!-- Not Found -->
    <div
      v-else
      class="text-center py-12"
    >
      <VIcon
        icon="tabler-alert-circle"
        size="48"
        class="text-error mb-2"
      />
      <div class="text-h5 mb-2">
        Template Not Found
      </div>
      <p class="text-body-1 text-medium-emphasis mb-4">
        The template you're looking for doesn't exist or you don't have permission to view it.
      </p>
      <VBtn
        color="primary"
        @click="router.push('/rate-cards')"
      >
        Back to Rate Cards
      </VBtn>
    </div>

    <!-- Delete Confirmation Dialog -->
    <VDialog
      v-model="isDeleteDialogVisible"
      max-width="500"
    >
      <VCard>
        <VCardTitle>
          Delete Template
        </VCardTitle>
        <VCardText>
          Are you sure you want to delete "{{ template?.templateName }}"? This action cannot be undone.
        </VCardText>
        <VCardActions>
          <VSpacer />
          <VBtn
            color="secondary"
            variant="outlined"
            @click="isDeleteDialogVisible = false"
          >
            Cancel
          </VBtn>
          <VBtn
            color="error"
            @click="confirmDelete"
          >
            Delete
          </VBtn>
        </VCardActions>
      </VCard>
    </VDialog>
  </div>
</template>

<style lang="scss" scoped>
.gap-y-4 {
  row-gap: 1rem;
}

.gap-x-6 {
  column-gap: 1.5rem;
}
</style>
