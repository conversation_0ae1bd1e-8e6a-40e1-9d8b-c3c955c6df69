<script setup>
import { useSocketStore } from '@stores/auth'
import { useSocketQuoteStore } from '@stores/quotes'
import { useSocketInvoiceStore } from '@stores/quotes/invoices'
import { socket } from '@socket/socket'
import { useHead } from '@unhead/vue'
import moment from 'moment'
import { toast } from 'vue3-toastify'
import 'vue3-toastify/dist/index.css'
import { useTheme } from 'vuetify'

const store = useSocketStore()
const storeQuote = useSocketQuoteStore()
const storeInvoice = useSocketInvoiceStore()

const { user } = storeToRefs(store)
const { quotes, quote } = storeToRefs(storeQuote)
const { invoices } = storeToRefs(storeInvoice)

const router = useRouter()
const vuetifyTheme = useTheme()

useHead({
  title: 'Qwote Z | Quotes',
  meta: [
    {
      name: 'description',
      content: 'My page description',
    },
  ],
})

socket.on('allQuotes', data => {
  quotes.value = data.data
})

socket.on('deleteQuote', data => {
  console.log({ data })
  switch (data.status) {
  case 'success':
    quotes.value = data.data
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })
    break
  case 'error':
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
    break
  default:
    break
  }
})

socket.on('createInvoice', data => {
  console.log({ data })
  switch (data.status) {
  case 'success':
    invoices.value = data.data.invoices

    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })
    break
  case 'error':
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
    break
  default:
    break
  }
})

onMounted(() => {
  if (!quotes.value) {
    getQuotes()
  }
})

const search = ref('')
const deleteItem = ref(null)
const isDeleteDialogVisible = ref(false)

// headers
const headers = [
  {
    title: 'JOB',
    key: 'job.name',
  },
  {
    title: 'DATE',
    key: 'createdAt',
  },
  {
    title: 'STATUS',
    key: 'status',
  },
  {
    title: 'ACTIONS',
    key: 'actions',
  },
]

const resolveStatusColor = status => {
  if (status === 'Initial')
    return 'info'
  if (status === 'Quote Sent')
    return 'info'
  if (status === 'Estimate Sent')
    return 'info'
  if (status === 'Started')
    return 'primary'
  if (status === 'Invoice Sent')
    return 'primary'
  if (status === 'On Hold')
    return 'warning'
  if (status === 'Overdue')
    return 'error'
  if (status === 'Invoice Overdue')
    return 'error'
  if (status === 'Completed')
    return 'success'
  if (status === 'Cancelled')
    return 'secondary'
}

const getQuotes = () => {
  storeQuote.getQuotes({
    user: user.value._id,
  })
}

const convertToQuote = item => {
  storeInvoice.createInvoice({
    user: user.value._id,
    jobRef: item.job,
    invoice: {
      quoteNumber: item.quoteNumber,
      currency: item.currency,
      status: 'invoice',
      subTotal: item.subTotal,
      discount: item.discount,
      tax: item.tax,
      total: item.total,
      items: item.items,
      user: user.value._id,
      job: item.job,
    },
  })
}

const editQuoteItem = item => {
  console.log(item)
  quote.value = item

  router.push({ name: 'quotes-id', params: { id: item.id } })
}

const confirmDeleteQuoteItem = item => {
  deleteItem.value = item
  isDeleteDialogVisible.value = true
}

const deleteQuoteItem = () => {
  isDeleteDialogVisible.value = false

  storeQuote.deleteQuote({
    user: user.value._id,
    id: deleteItem.value.id,
  })
}
</script>

<template>
  <div>
    <VCard>
      <VCardText>
        <VRow>
          <VCol
            cols="12"
            offset-md="8"
            md="4"
          >
            <AppTextField
              v-model="search"
              placeholder="Search ..."
              append-inner-icon="tabler-search"
              single-line
              hide-details
              dense
              outlined
            />
          </VCol>
        </VRow>
      </VCardText>

      <!-- 👉 Data Table  -->
      <VDataTable
        :headers="headers"
        :items="quotes || []"
        :search="search"
        :items-per-page="5"
        class="text-no-wrap"
      >
        <template #item.status="{ item }">
          <VChip
            variant="outlined"
            :color="resolveStatusColor(item.status)"
            :class="`text-${resolveStatusColor(item.status)}`"
            size="small"
            class="font-weight-medium"
          >
            {{ item.status }}
          </VChip>
        </template>

        <template #item.createdAt="{ item }">
          {{ moment(item.createdAt).format('ddd, MMM Do YYYY, h:mm:ss A') }}
        </template>

        <template #item.actions="{ item }">
          <div class="d-flex gap-1">
            <IconBtn
              color="info"
              @click="convertToQuote(item)"
            >
              <VIcon icon="tabler-repeat-once" />
              <VTooltip
                activator="parent"
                location="top"
              >
                Convert To Invoice
              </VTooltip>
            </IconBtn>
            <IconBtn
              color="warning"
              @click="editQuoteItem(item)"
            >
              <VIcon icon="tabler-edit" />
            </IconBtn>
            <IconBtn
              color="error"
              @click="confirmDeleteQuoteItem(item)"
            >
              <VIcon icon="tabler-trash" />
            </IconBtn>
          </div>
        </template>
      </VDataTable>
    </VCard>
    <VDialog
      v-model="isDeleteDialogVisible"
      width="500"
    >
      <!-- Dialog close btn -->
      <DialogCloseBtn @click="isDeleteDialogVisible = !isDeleteDialogVisible" />

      <!-- Dialog Content -->
      <VCard title="Delete Job">
        <VCardText>
          <h4>Are you sure you want to delete this quote?</h4>
          <br>
          <h4 class="text-error">
            {{ deleteItem.quoteNumber }}
          </h4>
        </VCardText>

        <VCardText class="d-flex justify-end gap-3 flex-wrap">
          <VBtn
            color="secondary"
            variant="tonal"
            @click="isDeleteDialogVisible = false"
          >
            Cancel
          </VBtn>
          <VBtn
            color="error"
            @click="deleteQuoteItem"
          >
            Confirm
          </VBtn>
        </VCardText>
      </VCard>
    </VDialog>
  </div>
</template>
