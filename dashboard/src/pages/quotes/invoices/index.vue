<script setup>
import { useSocketStore } from '@stores/auth'
import { useSocketInvoiceStore } from '@stores/quotes/invoices'
import { socket } from '@socket/socket'
import { useHead } from '@unhead/vue'
import moment from 'moment'
import { toast } from 'vue3-toastify'
import 'vue3-toastify/dist/index.css'
import { useTheme } from 'vuetify'

const store = useSocketStore()
const storeInvoice = useSocketInvoiceStore()

const { user } = storeToRefs(store)
const { invoices, invoice } = storeToRefs(storeInvoice)

const router = useRouter()
const vuetifyTheme = useTheme()

useHead({
  title: 'Qwote Z | Invoices',
  meta: [
    {
      name: 'description',
      content: 'My page description',
    },
  ],
})

socket.on('allInvoices', data => {
  invoices.value = data.data
})

socket.on('deleteInvoice', data => {
  console.log({ data })
  switch (data.status) {
  case 'success':
    invoices.value = data.data
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })
    break
  case 'error':
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
    break
  default:
    break
  }
})

onMounted(() => {
  if (!invoices.value) {
    getInvoices()
  }
})

const search = ref('')
const deleteItem = ref(null)
const isDeleteDialogVisible = ref(false)

// headers
const headers = [
  {
    title: 'JOB',
    key: 'job.name',
  },
  {
    title: 'DATE',
    key: 'createdAt',
  },
  {
    title: 'STATUS',
    key: 'status',
  },
  {
    title: 'ACTIONS',
    key: 'actions',
  },
]

const resolveStatusColor = status => {
  if (status === 'Initial' || status === 'Quote Sent' || status === 'Estimate Sent')
    return 'info'
  if (status === 'Started' || status === 'Invoice Sent')
    return 'primary'
  if (status === 'On Hold')
    return 'warning'
  if (status === 'Overdue' || status === 'Invoice Overdue')
    return 'error'
  if (status === 'Completed' || status === 'Paid')
    return 'success'
  if (status === 'Cancelled')
    return 'secondary'
}

const getInvoices = () => {
  storeInvoice.getInvoices({
    user: user.value._id,
  })
}

const editInvoiceItem = item => {
  console.log(item)
  invoice.value = item

  router.push({ name: 'quotes-invoices-id', params: { id: item.id } })
}

const confirmDeleteInvoiceItem = item => {
  deleteItem.value = item
  isDeleteDialogVisible.value = true
}

const deleteInvoiceItem = () => {
  isDeleteDialogVisible.value = false

  storeInvoice.deleteInvoice({
    user: user.value._id,
    id: deleteItem.value.id,
  })
}
</script>

<template>
  <div>
    <VCard>
      <VCardText>
        <VRow>
          <VCol
            cols="12"
            offset-md="8"
            md="4"
          >
            <AppTextField
              v-model="search"
              placeholder="Search ..."
              append-inner-icon="tabler-search"
              single-line
              hide-details
              dense
              outlined
            />
          </VCol>
        </VRow>
      </VCardText>

      <!-- 👉 Data Table  -->
      <VDataTable
        :headers="headers"
        :items="invoices || []"
        :search="search"
        :items-per-page="5"
        class="text-no-wrap"
      >
        <template #item.status="{ item }">
          <VChip
            variant="outlined"
            :color="resolveStatusColor(item.status)"
            :class="`text-${resolveStatusColor(item.status)}`"
            size="small"
            class="font-weight-medium"
          >
            {{ item.status }}
          </VChip>
        </template>

        <template #item.createdAt="{ item }">
          {{ moment(item.createdAt).format('ddd, MMM Do YYYY, h:mm:ss A') }}
        </template>

        <template #item.actions="{ item }">
          <div class="d-flex gap-1">
            <IconBtn
              color="warning"
              @click="editInvoiceItem(item)"
            >
              <VIcon icon="tabler-edit" />
            </IconBtn>
            <IconBtn
              color="error"
              @click="confirmDeleteInvoiceItem(item)"
            >
              <VIcon icon="tabler-trash" />
            </IconBtn>
          </div>
        </template>
      </VDataTable>
    </VCard>
    <VDialog
      v-model="isDeleteDialogVisible"
      width="500"
    >
      <!-- Dialog close btn -->
      <DialogCloseBtn @click="isDeleteDialogVisible = !isDeleteDialogVisible" />

      <!-- Dialog Content -->
      <VCard title="Delete Job">
        <VCardText>
          <h4>Are you sure you want to delete this quote?</h4>
          <br>
          <h4 class="text-error">
            {{ deleteItem.invoiceNumber }}
          </h4>
        </VCardText>

        <VCardText class="d-flex justify-end gap-3 flex-wrap">
          <VBtn
            color="secondary"
            variant="tonal"
            @click="isDeleteDialogVisible = false"
          >
            Cancel
          </VBtn>
          <VBtn
            color="error"
            @click="deleteInvoiceItem"
          >
            Confirm
          </VBtn>
        </VCardText>
      </VCard>
    </VDialog>
  </div>
</template>
