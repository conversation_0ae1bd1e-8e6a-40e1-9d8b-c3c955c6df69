<script setup lang="ts">
import { useSocketStore } from '@stores/auth'
import { useSocketClientStore } from '@stores/clients/client'
import { socket } from '@socket/socket'
import { toast } from 'vue3-toastify'
import 'vue3-toastify/dist/index.css'
import { useTheme } from 'vuetify'
import { useHead } from '@unhead/vue'
import { storeToRefs } from 'pinia'

const store = useSocketStore()
const storeClient = useSocketClientStore()

const { user } = storeToRefs(store)
const { clients, client } = storeToRefs(storeClient)

const vuetifyTheme = useTheme()

useHead({
  title: 'Qwote Z | Create Client',
  meta: [
    {
      name: 'description',
      content: 'My page description',
    },
  ],
})

socket.on('createClient', data => {
  console.log({ data })
  switch (data.status) {
  case 'success':
    client.value = data.data.client
    clients.value = data.data.clients

    name.value = ''
    lastName.value = ''
    phone.value = ''
    address.value = ''
    websiteUrl.value = ''
    company.value = ''
    email.value = ''
    socialMedia.value = [{
      facebook: '',
      instagram: '',
      tiktok: '',
      youtube: '',
    }]
    timezone.value = ''
    currency.value = ''
    location.value = ''
    designation.value = ''
    language.value = []

    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })
    break
  case 'error':
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
    break
  default:
    break
  }
})

const currencies = [
  'AUD',
  'CAD',
  'EUR',
  'GBP',
  'USD',
  'ZAR',
]

const languages = [
  'Afrikaans',
  'English',
  'Japanese',
  'Russian',
]

const timezones = [
  { 'offset': 'UTC-12:00', 'timezone': 'Etc/GMT+12' },
  { 'offset': 'UTC-11:00', 'timezone': 'Pacific/Pago_Pago' },
  { 'offset': 'UTC-10:00', 'timezone': 'Pacific/Honolulu' },
  { 'offset': 'UTC-09:30', 'timezone': 'Pacific/Marquesas' },
  { 'offset': 'UTC-09:00', 'timezone': 'America/Anchorage' },
  { 'offset': 'UTC-08:00', 'timezone': 'America/Los_Angeles' },
  { 'offset': 'UTC-07:00', 'timezone': 'America/Denver' },
  { 'offset': 'UTC-06:00', 'timezone': 'America/Chicago' },
  { 'offset': 'UTC-05:00', 'timezone': 'America/New_York' },
  { 'offset': 'UTC-04:00', 'timezone': 'America/Halifax' },
  { 'offset': 'UTC-03:30', 'timezone': 'America/St_Johns' },
  { 'offset': 'UTC-03:00', 'timezone': 'America/Argentina/Buenos_Aires' },
  { 'offset': 'UTC-02:00', 'timezone': 'America/Noronha' },
  { 'offset': 'UTC-01:00', 'timezone': 'Atlantic/Azores' },
  { 'offset': 'UTC+00:00', 'timezone': 'UTC' },
  { 'offset': 'UTC+01:00', 'timezone': 'Europe/Berlin' },
  { 'offset': 'UTC+02:00', 'timezone': 'Europe/Athens' },
  { 'offset': 'UTC+03:00', 'timezone': 'Europe/Moscow' },
  { 'offset': 'UTC+03:30', 'timezone': 'Asia/Tehran' },
  { 'offset': 'UTC+04:00', 'timezone': 'Asia/Dubai' },
  { 'offset': 'UTC+04:30', 'timezone': 'Asia/Kabul' },
  { 'offset': 'UTC+05:00', 'timezone': 'Asia/Karachi' },
  { 'offset': 'UTC+05:30', 'timezone': 'Asia/Kolkata' },
  { 'offset': 'UTC+05:45', 'timezone': 'Asia/Kathmandu' },
  { 'offset': 'UTC+06:00', 'timezone': 'Asia/Dhaka' },
  { 'offset': 'UTC+06:30', 'timezone': 'Asia/Yangon' },
  { 'offset': 'UTC+07:00', 'timezone': 'Asia/Bangkok' },
  { 'offset': 'UTC+08:00', 'timezone': 'Asia/Shanghai' },
  { 'offset': 'UTC+08:45', 'timezone': 'Australia/Eucla' },
  { 'offset': 'UTC+09:00', 'timezone': 'Asia/Tokyo' },
  { 'offset': 'UTC+09:30', 'timezone': 'Australia/Adelaide' },
  { 'offset': 'UTC+10:00', 'timezone': 'Australia/Sydney' },
  { 'offset': 'UTC+10:30', 'timezone': 'Australia/Lord_Howe' },
  { 'offset': 'UTC+11:00', 'timezone': 'Pacific/Noumea' },
  { 'offset': 'UTC+12:00', 'timezone': 'Pacific/Auckland' },
  { 'offset': 'UTC+12:45', 'timezone': 'Pacific/Chatham' },
  { 'offset': 'UTC+13:00', 'timezone': 'Pacific/Tongatapu' },
  { 'offset': 'UTC+14:00', 'timezone': 'Pacific/Kiritimati' },
]

const name = ref('')
const lastName = ref('')
const phone = ref('')
const address = ref('')
const websiteUrl = ref('')
const company = ref('')
const email = ref('')
const identity = ref('')

const socialMedia = ref([{
  facebook: '',
  instagram: '',
  tiktok: '',
  youtube: '',
}])

const timezone = ref()
const currency = ref('')
const location = ref('')
const designation = ref('')
const language = ref([])

const createClient = () => {
  storeClient.createClient({
    user: user.value._id,
    identity: identity.value,
    name: name.value,
    lastName: lastName.value,
    phone: phone.value,
    email: email.value,
    address: address.value,
    websiteUrl: websiteUrl.value,
    company: company.value,
    socialMedia: socialMedia.value,
    timezone: timezone.value,
    currency: currency.value,
    location: location.value,
    designation: designation.value,
    language: language.value,
  })
}
</script>

<template>
  <VCard>
    <VCardText>
      <VForm @submit.prevent="createClient">
        <VRow>
          <!-- 👉 First Name -->
          <VCol
            cols="12"
            md="6"
          >
            <AppTextField
              v-model="name"
              label="First Name"
              placeholder="John"
            />
          </VCol>

          <!-- 👉 Last Name -->
          <VCol
            cols="12"
            md="6"
          >
            <AppTextField
              v-model="lastName"
              label="Last Name"
              placeholder="Doe"
            />
          </VCol>

          <!-- 👉 phone -->
          <VCol
            cols="12"
            md="6"
          >
            <AppTextField
              v-model="phone"
              label="Phone Number"
              placeholder="Phone Number"
            />
          </VCol>

          <!-- 👉 Email -->
          <VCol
            cols="12"
            md="6"
          >
            <AppTextField
              v-model="email"
              label="Email"
              type="email"
              placeholder="<EMAIL>"
            />
          </VCol>

          <!-- 👉 Address -->
          <VCol cols="12">
            <AppTextarea
              v-model="address"
              label="Address"
              placeholder="Address"
            />
          </VCol>

          <!-- 👉 websiteUrl -->
          <VCol
            cols="12"
            md="6"
          >
            <AppTextField
              v-model="websiteUrl"
              label="Website"
              placeholder="https://domain.com"
            />
          </VCol>

          <!-- 👉 designation -->
          <VCol
            cols="12"
            md="6"
          >
            <AppTextField
              v-model="designation"
              label="Designation"
              placeholder="Owner"
            />
          </VCol>

          <!-- 👉 Company -->
          <VCol
            cols="12"
            md="6"
          >
            <AppTextField
              v-model="company"
              label="Company Name"
              placeholder="Company Name"
            />
          </VCol>

          <!-- 👉 currency -->
          <VCol
            cols="12"
            md="6"
          >
            <AppSelect
              v-model="currency"
              :items="currencies"
              placeholder="Select Currency"
              label="Currency"
            />
          </VCol>

          <!-- 👉 location -->
          <VCol
            cols="12"
            md="6"
          >
            <AppTextField
              v-model="location"
              label="Location"
              placeholder="New York"
            />
          </VCol>

          <!-- 👉 timezone -->
          <VCol
            cols="12"
            md="6"
          >
            <AppSelect
              v-model="timezone"
              :items="timezones"
              item-title="offset"
              item-value="offset"
              placeholder="Select Time Zone"
              label="Time Zone"
            />
          </VCol>

          <!-- 👉 language -->
          <VCol
            cols="12"
            md="6"
          >
            <AppSelect
              v-model="language"
              :items="languages"
              placeholder="Select Language"
              label="Language"
              chips
              multiple
              closable-chips
            />
          </VCol>

          <VCol cols="12">
            <AppSelect
              v-model="identity"
              :items="user.identities"
              item-title="name"
              item-value="_id"
              label="Select Identity to Assign Currency"
              placeholder="Select Identity"
            />
          </VCol>

          <VCol
            cols="12"
            class="d-flex gap-4"
          >
            <VBtn type="submit">
              Submit
            </VBtn>

            <VBtn
              type="reset"
              color="secondary"
              variant="tonal"
            >
              Reset
            </VBtn>
          </VCol>
        </VRow>
      </VForm>
    </VCardText>
  </VCard>
</template>

<style scoped lang="scss">
//
</style>
