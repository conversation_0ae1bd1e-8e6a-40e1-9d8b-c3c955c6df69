<script setup>
import RateItem from './RateItem.vue'
import { VNodeRenderer } from '@layouts/components/VNodeRenderer'
import { themeConfig } from '@themeConfig'

const props = defineProps({
  data: {
    type: null,
    required: true,
  },
  rateType: {
    type: String,
    required: true,
  },
})

const emit = defineEmits([
  'push',
  'remove',
])

// 👉 Add item function
const addItem = () => {
  console.log('add item')
  emit('push', {
    name: '',
    description: '',
    rate: 0,
  })
}

const removeRate = id => {
  console.log({ id })
  emit('remove', id)
}
</script>

<template>
  <!-- SECTION Header -->
  <div class="d-flex flex-wrap justify-space-between flex-column rounded bg-var-theme-background  gap-6 pa-6 mb-6">
    <!-- 👉 Add rates -->
    <div class="add-products-form">
      <div
        v-for="(rate, index) in props.data"
        :key="rate.name"
        class="mb-4"
      >
        <RateItem
          :id="index"
          :data="rate"
          :rate-type="props.rateType"
          @remove-rate="removeRate"
        />
      </div>
      <pre>{{ props.data }}</pre>

      <VBtn
        size="small"
        prepend-icon="tabler-plus"
        @click="addItem"
      >
        Add Item
      </VBtn>
    </div>

    <VDivider class="my-6 border-dashed" />
  </div>
</template>
