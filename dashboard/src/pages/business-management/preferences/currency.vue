<script setup>
import { useSocketStore } from '@stores/auth'
import { useSocketCurrencyStore } from '@stores/preferences/currency'
import { socket } from '@socket/socket'
import { toast } from 'vue3-toastify'
import 'vue3-toastify/dist/index.css'
import { useTheme } from 'vuetify'
import { useHead } from '@unhead/vue'

const store = useSocketStore()
const storeCurrency = useSocketCurrencyStore()

const { user } = storeToRefs(store)
const { currencies, currency } = storeToRefs(storeCurrency)

const vuetifyTheme = useTheme()

useHead({
  title: 'Qwote Z | Currencies',
  meta: [
    {
      name: 'description',
      content: 'My page description',
    },
  ],
})

socket.on('allCurrencies', data => {
  currencies.value = data.data
})

socket.on('createCurrency', data => {
  console.log({ data })
  switch (data.status) {
  case 'success':
    currency.value = data.data.currency
    currencies.value = data.data.currencies

    name.value = ''
    currencyCode.value= ''

    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })

    isCurrencyDialog.value = false
    break
  case 'error':
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
    break
  default:
    break
  }
})

socket.on('updateCurrency', data => {
  console.log({ data })
  switch (data.status) {
  case 'success':
    currency.value = data.data.currency
    currencies.value = data.data.currencies

    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })

    isEditCurrencyDialog.value = false
    break
  case 'error':
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
    break
  default:
    break
  }
})

socket.on('deleteCurrency', data => {
  console.log({ data })
  switch (data.status) {
  case 'success':
    currencies.value = data.data

    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })

    isDeleteCurrencyDialog.value = false
    break
  case 'error':
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
    break
  default:
    break
  }
})

onMounted(() => {
  if (!currencies.value) {
    storeCurrency.getCurrencies({
      user: user.value._id,
    })
  }
})

const search = ref('')
const isCurrencyDialog = ref(false)
const isEditCurrencyDialog = ref(false)
const isDeleteCurrencyDialog = ref(false)

const currencyNames = ref([
  'Australian Dollar',
  'Canadian Dollar',
  'Euro',
  'British Pound',
  'US Dollar',
  'South African Rand',
])

const currencyCodes = ref([
  'AUD',
  'CAD',
  'EUR',
  'GBP',
  'USD',
  'ZAR',
])

const name = ref('')
const currencyCode = ref('')
const identityAccount = ref('')

// headers
const headers = [
  {
    title: 'Name',
    key: 'name',
    sortable: true,
  },
  {
    title: 'Currency Code',
    key: 'currencyCode',
    sortable: true,
  },
  {
    title: 'Identity',
    key: 'identity',
    sortable: true,
  },
  {
    title: 'Actions',
    key: 'actions',
    sortable: false,
  },
]

const createCurrency = () => {
  storeCurrency.createCurrency({
    user: user.value._id,
    identity: identityAccount.value,
    name: name.value,
    currencyCode: currencyCode.value,
  })
}

const editItem = item => {
  currency.value = item
  isEditCurrencyDialog.value = true
}

const updateCurrency = () => {
  storeCurrency.updateCurrency({
    user: user.value._id,
    id: currency.value._id,
    identity: currency.value.identity._id,
    name: currency.value.name,
    currencyCode: currencyCode.value,
  })
}

const deleteItem = item => {
  console.log({ item })
  currency.value = item

  isDeleteCurrencyDialog.value = true
}

const deleteCurrency = () => {
  storeCurrency.deleteCurrency({
    user: user.value._id,
    id: currency.value._id,
    identity: currency.value.identity._id,
  })
}

const resolveStatusColor = identity => {
  if (identity === user.value.profile.defaultIdentity.name) {
    return 'primary'
  } else {
    return 'secondary'
  }
}

// eslint-disable-next-line vue/return-in-computed-property
const selectedCurrencyCode = computed(() => {
  switch (name.value) {
  case 'Australian Dollar':
    return currencyCode.value = 'AUD'
  case 'Canadian Dollar':
    return currencyCode.value = 'CAD'
  case 'Euro':
    return currencyCode.value = 'EUR'
  case 'British Pound':
    return currencyCode.value = 'GBP'
  case 'US Dollar':
    return currencyCode.value = 'USD'
  case 'South African Rand':
    return currencyCode.value = 'ZAR'
  default:
    break
  }
})

// eslint-disable-next-line vue/return-in-computed-property
const selectedEditCurrencyCode = computed(() => {
  switch (currency.value.name) {
  case 'Australian Dollar':
    return currencyCode.value = 'AUD'
  case 'Canadian Dollar':
    return currencyCode.value = 'CAD'
  case 'Euro':
    return currencyCode.value = 'EUR'
  case 'British Pound':
    return currencyCode.value = 'GBP'
  case 'US Dollar':
    return currencyCode.value = 'USD'
  case 'South African Rand':
    return currencyCode.value = 'ZAR'
  default:
    break
  }
})
</script>

<template>
  <VCard>
    <VCardText>
      <VRow>
        <VCol>
          <VBtn @click="isCurrencyDialog = !isCurrencyDialog">
            Create Currency
          </VBtn>
        </VCol>
        <VCol
          class="text-end"
          cols="8"
          md="4"
        >
          <AppTextField
            v-model="search"
            placeholder="Search ..."
            append-inner-icon="tabler-search"
            single-line
            hide-details
            dense
            outlined
          />
        </VCol>
      </VRow>
    </VCardText>

    <!-- 👉 Data Table  -->
    <VDataTable
      :headers="headers"
      :items="currencies || []"
      :search="search"
      :items-per-page="5"
      class="text-no-wrap"
    >
      <!-- Identity -->
      <template #item.identity="{ item }">
        <VChip
          variant="outlined"
          :color="resolveStatusColor(item.identity.name)"
          :class="`text-${resolveStatusColor(item.identity.name)}`"
          size="small"
          class="font-weight-medium"
        >
          {{ item.identity.name }}
        </VChip>
      </template>

      <!-- Actions -->
      <template #item.actions="{ item }">
        <IconBtn @click="editItem(item)">
          <VIcon
            color="warning"
            icon="tabler-edit"
          />
        </IconBtn>
        <IconBtn @click="deleteItem(item)">
          <VIcon
            color="error"
            icon="tabler-trash"
          />
        </IconBtn>
      </template>
    </VDataTable>
  </VCard>

  <!-- Create -->
  <VDialog
    v-model="isCurrencyDialog"
    max-width="600"
  >
    <!-- Dialog close btn -->
    <DialogCloseBtn @click="isCurrencyDialog = !isCurrencyDialog" />

    <!-- Dialog Content -->
    <VCard title="Create Currency">
      <VCardText>
        <VRow>
          <VCol cols="6">
            <AppSelect
              v-model="name"
              :items="currencyNames"
              label="Select Currency"
              placeholder="Select Currency"
            />
          </VCol>
          <VCol cols="6">
            <AppSelect
              v-model="selectedCurrencyCode"
              :items="currencyCodes"
              label="Currency Code"
              placeholder="Currency Code"
              disabled
            />
          </VCol>
          <VCol cols="12">
            <AppSelect
              v-model="identityAccount"
              :items="user.identities"
              item-title="name"
              item-value="_id"
              label="Select Identity to Assign Currency"
              placeholder="Select Identity"
            />
          </VCol>
        </VRow>
      </VCardText>

      <VCardText class="d-flex justify-end flex-wrap gap-3">
        <VBtn
          variant="tonal"
          color="secondary"
          @click="isCurrencyDialog = false"
        >
          Close
        </VBtn>
        <VBtn @click="createCurrency">
          Create Currency
        </VBtn>
      </VCardText>
    </VCard>
  </VDialog>

  <!-- Edit -->
  <VDialog
    v-model="isEditCurrencyDialog"
    max-width="600"
  >
    <!-- Dialog close btn -->
    <DialogCloseBtn @click="isEditCurrencyDialog = !isEditCurrencyDialog" />

    <!-- Dialog Content -->
    <VCard title="Update Currency">
      <VCardText>
        <VRow>
          <VCol cols="6">
            <AppSelect
              v-model="currency.name"
              :items="currencyNames"
              label="Select Currency"
              placeholder="Select Currency"
            />
          </VCol>
          <VCol cols="6">
            <AppSelect
              v-model="selectedEditCurrencyCode"
              :items="currencyCodes"
              label="Currency Code"
              placeholder="Currency Code"
              disabled
            />
          </VCol>
          <VCol cols="12">
            <AppSelect
              v-model="currency.identity"
              :items="user.identities"
              item-title="name"
              item-value="_id"
              label="Select Identity to Assign Currency"
              placeholder="Select Identity"
              disabled
            />
          </VCol>
        </VRow>
      </VCardText>

      <VCardText class="d-flex justify-end flex-wrap gap-3">
        <VBtn
          variant="tonal"
          color="secondary"
          @click="isEditCurrencyDialog = false"
        >
          Close
        </VBtn>
        <VBtn @click="updateCurrency">
          Update Currency
        </VBtn>
      </VCardText>
    </VCard>
  </VDialog>

  <!-- Delete -->
  <VDialog
    v-model="isDeleteCurrencyDialog"
    max-width="600"
  >
    <!-- Dialog close btn -->
    <DialogCloseBtn @click="isDeleteCurrencyDialog = !isDeleteCurrencyDialog" />

    <!-- Dialog Content -->
    <VCard title="Delete Currency">
      <VCardText>
        You are about to delete Currency <b class="text-error">{{ currency.name }} | {{ currency.currencyCode }}</b>
      </VCardText>

      <VCardText class="d-flex justify-end flex-wrap gap-3">
        <VBtn
          variant="tonal"
          color="secondary"
          @click="isDeleteCurrencyDialog = false"
        >
          Close
        </VBtn>
        <VBtn
          color="error"
          @click="deleteCurrency"
        >
          Delete Currency
        </VBtn>
      </VCardText>
    </VCard>
  </VDialog>
</template>

<style scoped lang="scss">
//
</style>
