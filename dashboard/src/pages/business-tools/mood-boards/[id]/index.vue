<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { storeToRefs } from 'pinia'
import { useSocketStore } from '@stores/auth'
import { useSocketMoodBoardStore } from '@stores/mood-boards'
import { socket } from '@socket/socket'
import { toast } from 'vue3-toastify'
import 'vue3-toastify/dist/index.css'
import { useTheme } from 'vuetify'
import { useHead } from '@unhead/vue'

const route = useRoute()
const storeAuth = useSocketStore()
const moodBoardStore = useSocketMoodBoardStore()

const { user } = storeToRefs(storeAuth)
const { moodBoard } = storeToRefs(moodBoardStore)

const vuetifyTheme = useTheme()
const router = useRouter()

useHead({
  title: computed(() => moodBoard.value ? `${moodBoard.value.title} | Qwote Z` : 'Qwote Z | Mood Board'),
  meta: [
    {
      name: 'description',
      content: computed(() => moodBoard.value?.description || 'View mood board'),
    },
  ],
})

// State
const moodBoardId = ref(route.params.id)
const loading = ref(true)
const showShareDialog = ref(false)

// Socket event listeners
socket.on('singleMoodBoard', data => {
  loading.value = false
  if (data.status === 'success') {
    moodBoard.value = data.data
  } else {
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
    router.push({ name: 'business-tools-mood-boards' })
  }
})

// Load mood board on mount
onMounted(() => {
  if (moodBoardId.value) {
    moodBoardStore.getMoodBoard({
      user: user.value._id,
      id: moodBoardId.value,
    })
  }
})

// Methods
function handleEdit() {
  router.push({ name: 'business-tools-mood-boards-id-edit', params: { id: moodBoard.value._id } })
}

function handleShare() {
  showShareDialog.value = true
}

function copyShareLink() {
  const shareUrl = `${window.location.origin}/business-tools/mood-boards/${moodBoard.value._id}`

  navigator.clipboard.writeText(shareUrl).then(() => {
    toast('Share link copied to clipboard!', {
      autoClose: 3000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })
  })
}

function shareOnSocial(platform) {
  const url = encodeURIComponent(`${window.location.origin}/business-tools/mood-boards/${moodBoard.value._id}`)
  const text = encodeURIComponent(`Check out this mood board: ${moodBoard.value.title}`)
  
  let shareUrl = ''
  
  switch (platform) {
  case 'facebook':
    shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${url}`
    break
  case 'twitter':
    shareUrl = `https://twitter.com/intent/tweet?url=${url}&text=${text}`
    break
  case 'linkedin':
    shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${url}`
    break
  default:
    return
  }
  
  window.open(shareUrl, '_blank', 'width=600,height=400')
}

function formatDate(date) {
  return new Date(date).toLocaleDateString()
}

function getItemStyle(item) {
  return {
    position: 'absolute',
    left: `${item.position.x}px`,
    top: `${item.position.y}px`,
    width: `${item.size.width}px`,
    height: `${item.size.height}px`,
    transform: `rotate(${item.style.rotation || 0}deg)`,
    opacity: item.style.opacity || 1,
    zIndex: item.style.zIndex || 1,
    border: item.style.borderWidth ? 
      `${item.style.borderWidth}px solid ${item.style.borderColor || '#000'}` : 'none',
    borderRadius: `${item.style.borderRadius || 0}px`,
    backgroundColor: item.style.backgroundColor || 'transparent',
  }
}

function getTextStyle(item) {
  return {
    fontSize: `${item.metadata.fontSize || 16}px`,
    fontFamily: item.metadata.fontFamily || 'Arial, sans-serif',
    fontWeight: item.metadata.fontWeight || 'normal',
    textAlign: item.metadata.textAlign || 'left',
    color: item.metadata.color || '#000000',
    width: '100%',
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: item.metadata.textAlign === 'center' ? 'center' : 
      item.metadata.textAlign === 'right' ? 'flex-end' : 'flex-start',
    padding: '8px',
    wordWrap: 'break-word',
    overflow: 'hidden',
  }
}

const canvasStyle = computed(() => {
  if (!moodBoard.value) return {}
  
  return {
    width: `${moodBoard.value.settings.canvasSize.width}px`,
    height: `${moodBoard.value.settings.canvasSize.height}px`,
    backgroundColor: moodBoard.value.settings.backgroundColor,
    backgroundImage: moodBoard.value.settings.showGrid ? 
      'radial-gradient(circle, #ccc 1px, transparent 1px)' : 'none',
    backgroundSize: moodBoard.value.settings.showGrid ? 
      `${moodBoard.value.settings.gridSize}px ${moodBoard.value.settings.gridSize}px` : 'auto',
  }
})

const isOwner = computed(() => {
  return moodBoard.value && user.value && moodBoard.value.user._id === user.value._id
})
</script>

<template>
  <div class="mood-board-viewer">
    <!-- Loading State -->
    <div
      v-if="loading"
      class="d-flex align-center justify-center"
      style="height: 100vh;"
    >
      <VProgressCircular
        indeterminate
        color="primary"
        size="48"
      />
      <div class="ms-4 text-h6">
        Loading mood board...
      </div>
    </div>

    <!-- Mood Board View -->
    <div
      v-else-if="moodBoard"
      class="mood-board-container"
    >
      <!-- Header -->
      <VAppBar
        color="white"
        elevation="1"
        height="64"
      >
        <VContainer
          fluid
          class="d-flex align-center"
        >
          <VBtn
            icon
            variant="text"
            to="/business-tools/mood-boards"
          >
            <VIcon icon="tabler-arrow-left" />
          </VBtn>
          
          <div class="ms-4">
            <h6 class="text-h6 font-weight-medium">
              {{ moodBoard.title }}
            </h6>
            <div class="text-caption text-medium-emphasis">
              by {{ moodBoard.user.name }} • {{ formatDate(moodBoard.createdAt) }}
            </div>
          </div>

          <VSpacer />

          <div class="d-flex align-center gap-2">
            <VBtn
              variant="outlined"
              size="small"
              prepend-icon="tabler-share"
              @click="handleShare"
            >
              Share
            </VBtn>
            
            <VBtn
              v-if="isOwner"
              color="primary"
              size="small"
              prepend-icon="tabler-edit"
              @click="handleEdit"
            >
              Edit
            </VBtn>
          </div>
        </VContainer>
      </VAppBar>

      <!-- Mood Board Info -->
      <div
        v-if="moodBoard.description || moodBoard.tags?.length"
        class="mood-board-info"
      >
        <VContainer>
          <VCard variant="outlined">
            <VCardText>
              <p
                v-if="moodBoard.description"
                class="text-body-1 mb-3"
              >
                {{ moodBoard.description }}
              </p>
              
              <div
                v-if="moodBoard.tags?.length"
                class="d-flex flex-wrap gap-2"
              >
                <VChip
                  v-for="tag in moodBoard.tags"
                  :key="tag"
                  size="small"
                  variant="tonal"
                >
                  {{ tag }}
                </VChip>
              </div>
            </VCardText>
          </VCard>
        </VContainer>
      </div>

      <!-- Canvas -->
      <div class="mood-board-canvas-wrapper">
        <div
          class="mood-board-canvas"
          :style="canvasStyle"
        >
          <!-- Render mood board items -->
          <div
            v-for="item in moodBoard.items"
            :key="item._id"
            class="mood-board-item"
            :style="getItemStyle(item)"
          >
            <!-- Image Item -->
            <img
              v-if="item.type === 'image'"
              :src="item.content"
              :alt="item.metadata.title || 'Mood board image'"
              class="mood-board-image"
            >
            
            <!-- Color Item -->
            <div
              v-else-if="item.type === 'color'"
              class="mood-board-color"
              :style="{ backgroundColor: item.content }"
            />
            
            <!-- Text Item -->
            <div
              v-else-if="item.type === 'text'"
              class="mood-board-text"
              :style="getTextStyle(item)"
            >
              {{ item.content }}
            </div>
            
            <!-- Link Item -->
            <a
              v-else-if="item.type === 'link'"
              :href="item.content"
              target="_blank"
              rel="noopener noreferrer"
              class="mood-board-link"
            >
              <VIcon icon="tabler-link" />
              <span class="link-text">{{ item.metadata.title || item.content }}</span>
            </a>
          </div>
        </div>
      </div>

      <!-- Share Dialog -->
      <VDialog
        v-model="showShareDialog"
        max-width="500"
      >
        <VCard>
          <VCardTitle>Share Mood Board</VCardTitle>
          <VCardText>
            <div class="mb-4">
              <VLabel class="mb-2">
                Share Link
              </VLabel>
              <VTextField
                :model-value="`${window.location.origin}/business-tools/mood-boards/${moodBoard._id}`"
                readonly
                variant="outlined"
                density="compact"
              >
                <template #append-inner>
                  <VBtn
                    variant="text"
                    size="small"
                    @click="copyShareLink"
                  >
                    Copy
                  </VBtn>
                </template>
              </VTextField>
            </div>
            
            <div class="d-flex gap-2">
              <VBtn
                color="primary"
                variant="outlined"
                prepend-icon="tabler-brand-facebook"
                @click="shareOnSocial('facebook')"
              >
                Facebook
              </VBtn>
              
              <VBtn
                color="primary"
                variant="outlined"
                prepend-icon="tabler-brand-twitter"
                @click="shareOnSocial('twitter')"
              >
                Twitter
              </VBtn>
              
              <VBtn
                color="primary"
                variant="outlined"
                prepend-icon="tabler-brand-linkedin"
                @click="shareOnSocial('linkedin')"
              >
                LinkedIn
              </VBtn>
            </div>
          </VCardText>
          <VCardActions>
            <VSpacer />
            <VBtn @click="showShareDialog = false">
              Close
            </VBtn>
          </VCardActions>
        </VCard>
      </VDialog>
    </div>

    <!-- Error State -->
    <div
      v-else
      class="d-flex align-center justify-center flex-column"
      style="height: 100vh;"
    >
      <VIcon
        icon="tabler-alert-circle"
        size="64"
        color="error"
        class="mb-4"
      />
      <h3 class="text-h6 mb-2">
        Mood Board Not Found
      </h3>
      <p class="text-body-2 text-medium-emphasis mb-4">
        The mood board you're looking for doesn't exist or you don't have access to it.
      </p>
      <VBtn
        color="primary"
        to="/business-tools/mood-boards"
      >
        Back to Mood Boards
      </VBtn>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.mood-board-viewer {
  min-height: 100vh;
  background: #f5f5f5;
}

.mood-board-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.mood-board-info {
  padding: 20px 0;
  background: white;
  border-bottom: 1px solid #e0e0e0;
}

.mood-board-canvas-wrapper {
  flex: 1;
  padding: 20px;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.mood-board-canvas {
  position: relative;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  margin: 20px;
}

.mood-board-item {
  user-select: none;
}

.mood-board-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: inherit;
}

.mood-board-color {
  width: 100%;
  height: 100%;
  border-radius: inherit;
}

.mood-board-text {
  background: rgba(255, 255, 255, 0.9);
  border-radius: inherit;
}

.mood-board-link {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  text-decoration: none;
  color: inherit;
  
  &:hover {
    background: rgba(255, 255, 255, 1);
  }
  
  .link-text {
    font-size: 14px;
    color: rgb(var(--v-theme-primary));
  }
}
</style>
