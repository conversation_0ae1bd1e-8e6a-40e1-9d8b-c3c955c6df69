<script setup>
import KanbanBoardComp from '@/views/tools/kanban/KanbanBoard.vue'
import { useSocketStore } from '@stores/auth'
import { useSocketKanbanStore } from '@stores/kanban/index'
import { ref, onMounted } from 'vue'
import { socket } from '@socket/socket'
import { toast } from 'vue3-toastify'
import 'vue3-toastify/dist/index.css'
import { useTheme } from 'vuetify'

const store = useSocketStore()
const kanbanStore = useSocketKanbanStore()

const { user } = storeToRefs(store)
const { kanbans, kanban } = storeToRefs(kanbanStore)

const vuetifyTheme = useTheme()
const route = useRoute()

socket.on('addBoard', data => {
  console.log({ data })
  switch (data.status) {
  case 'success':
    kanbans.value = data.data.kanbans
    kanban.value = data.data.kanban

    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })

    break
  case 'error':
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
    break
  default:
    break
  }
})

socket.on('addItemKanban', data => {
  console.log({ data })
  switch (data.status) {
  case 'success':
    kanbans.value = data.data.kanbans
    kanban.value = data.data.kanban

    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })

    break
  case 'error':
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
    break
  default:
    break
  }
})

socket.on('updateItem', data => {
  console.log({ data })
  switch (data.status) {
  case 'success':
    kanban.value = data.data

    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })

    break
  case 'error':
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
    break
  default:
    break
  }
})

socket.on('updateItemState', data => {
  console.log({ data })
  switch (data.status) {
  case 'success':
    kanbans.value = data.data.kanbans
    kanban.value = data.data.kanban

    break
  case 'error':
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
    break
  default:
    break
  }
})

socket.on('updateBoardState', data => {
  console.log({ data })
  switch (data.status) {
  case 'success':
    kanbans.value = data.data.kanbans
    kanban.value = data.data.kanban

    break
  case 'error':
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
    break
  default:
    break
  }
})

socket.on('removeItemKanban', data => {
  console.log({ data })
  switch (data.status) {
  case 'success':
    kanbans.value = data.data.kanbans
    kanban.value = data.data.kanban

    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })

    break
  case 'error':
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
    break
  default:
    break
  }
})

socket.on('renameBoard', data => {
  console.log({ data })
  switch (data.status) {
  case 'success':
    kanbans.value = data.data.kanbans
    kanban.value = data.data.kanban

    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })

    break
  case 'error':
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
    break
  default:
    break
  }
})

socket.on('deleteKanbanBoard', data => {
  console.log({ data })
  switch (data.status) {
  case 'success':
    kanbans.value = data.data.kanbans
    kanban.value = data.data.kanban

    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })

    break
  case 'error':
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
    break
  default:
    break
  }
})

// Setup socket listeners
onMounted(() => {
  if (!kanban.value) {
    kanbanStore.getKanban({
      user: user.value._id,
      id: route.params.id,
    })
  }
})

const addNewBoard = async newBoardName => {
  console.log(newBoardName, kanban.value)
  kanbanStore.addBoard({
    user: user.value._id,
    id: kanban.value._id,
    title: newBoardName,
  })
}

const deleteBoard = async boardId => {
  kanbanStore.deleteKanbanBoard({
    user: user.value._id,
    kanbanId: route.params.id,
    boardId: boardId,
  })
}

const renameTheBoard = async kanbanBoard => {
  kanbanStore.renameBoard({
    user: user.value._id,
    id: route.params.id,
    kanbanBoard: kanbanBoard,
  })
}

const addNewItem = async newItem => {
  kanbanStore.addItemToKanban({
    user: user.value._id,
    id: kanban.value._id,
    item: newItem,
  })
}

const editItemFn = async editItem => {
  console.log('edit item', editItem)
  kanbanStore.updateItem({
    user: user.value._id,
    kanbanId: kanban.value._id,
    updateItem: editItem,
  })
}

const deleteItemFn = async deleteItem => {
  if (deleteItem.item && deleteItem.item._id) {
    kanbanStore.removeItemFromKanban({
      user: user.value._id,
      kanbanId: route.params.id,
      item: deleteItem,
    })
  }
}

const updateItemState = async kanbanState => {
  kanbanStore.updateItemState({
    user: user.value._id,
    kanbanId: route.params.id,
    updateItemState: kanbanState,
  })
}

const updateBoardState = async kanbanBoardIds => {
  console.log('update board state', kanbanBoardIds)
  kanbanStore.updateBoardState({
    user: user.value._id,
    kanbanId: route.params.id,
    kanbanBoardIds: kanbanBoardIds,
  })
}
</script>

<template>
  <KanbanBoardComp
    v-if="kanban"
    :kanban-data="{ boards: kanban.boards, items: kanban.items }"
    @add-new-board="addNewBoard"
    @delete-board="deleteBoard"
    @rename-board="renameTheBoard"
    @add-new-item="addNewItem"
    @edit-item="editItemFn"
    @delete-item="deleteItemFn"
    @update-items-state="updateItemState"
    @update-board-state="updateBoardState"
  />
</template>
