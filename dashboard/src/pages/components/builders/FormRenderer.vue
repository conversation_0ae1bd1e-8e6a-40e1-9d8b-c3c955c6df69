<script setup>
import { reactive, watch, toRefs } from 'vue'

const props = defineProps({
  schema: {
    type: Object,
    required: true,
  },
})

const formData = reactive({})

// Initialize form fields
watch(
  () => props.schema,
  newSchema => {
    newSchema.fields?.forEach(field => {
      if (field.type === 'file') {
        formData[field.model] = null
      } else if (field.type === 'slider' || field.type === 'rating') {
        formData[field.model] = 0
      } else if (field.type === 'switch' || field.type === 'checkbox') {
        formData[field.model] = false
      } else {
        formData[field.model] = ''
      }
    })
  },
  { immediate: true },
)

function submitForm() {
  console.log('Submitted Data:', { ...formData })
  alert(JSON.stringify(formData, null, 2))
}
</script>

<template>
  <VRow>
    <VCol>
      <h2>{{ schema.title }}</h2>
    </VCol>
  </VRow>

  <VRow>
    <VCol>
      <VForm @submit.prevent="submitForm">
        <div
          v-for="field in schema.fields"
          :key="field.model"
          class="mb-4"
        >
          <!-- Text & Email -->
          <VTextField
            v-if="field.type === 'text' || field.type === 'email'"
            v-model="formData[field.model]"
            :type="field.type"
            :label="field.label"
            :required="field.required"
          />

          <!-- Select -->
          <VSelect
            v-else-if="field.type === 'select'"
            v-model="formData[field.model]"
            :label="field.label"
            :items="field.options"
            :required="field.required"
          />

          <!-- Checkbox -->
          <VCheckbox
            v-else-if="field.type === 'checkbox'"
            v-model="formData[field.model]"
            :label="field.label"
            :required="field.required"
          />

          <!-- Radio -->
          <VRadioGroup
            v-else-if="field.type === 'radio'"
            v-model="formData[field.model]"
            :label="field.label"
            :required="field.required"
          >
            <VRadio
              v-for="option in field.options"
              :key="option"
              :label="option"
              :value="option"
            />
          </VRadioGroup>

          <!-- File Upload -->
          <VFileInput
            v-else-if="field.type === 'file'"
            v-model="formData[field.model]"
            :label="field.label"
            :required="field.required"
          />

          <!-- Slider -->
          <VSlider
            v-else-if="field.type === 'slider'"
            v-model="formData[field.model]"
            :label="field.label"
            :required="field.required"
          />

          <!-- Switch -->
          <VSwitch
            v-else-if="field.type === 'switch'"
            v-model="formData[field.model]"
            :label="field.label"
            :required="field.required"
          />

          <!-- Textarea -->
          <VTextarea
            v-else-if="field.type === 'textarea'"
            v-model="formData[field.model]"
            :label="field.label"
            :required="field.required"
          />

          <!-- Rating -->
          <VRating
            v-else-if="field.type === 'rating'"
            v-model="formData[field.model]"
            :length="5"
            :required="field.required"
          />
        </div>

        <VBtn
          type="submit"
          color="primary"
        >
          Submit
        </VBtn>
      </VForm>
    </VCol>
  </VRow>
</template>

<style scoped lang="scss">
//
</style>
