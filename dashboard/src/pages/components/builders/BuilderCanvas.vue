<script setup>
import { ref } from 'vue'
import { createField } from '@utils/schemaUtils'

const emit = defineEmits(['edit'])
const fields = ref([])

function onDrop(event) {
  const dropped = JSON.parse(event.dataTransfer.getData('field'))

  fields.value.push(createField(dropped.type, fields.value.length))
}

function edit(index) {
  emit('edit', index, fields.value[index])
}

defineExpose({ fields })
</script>

<template>
  <div
    class="builder drop-zone pa-4"
    style="min-height: 300px; border: 2px dashed #ccc;"
    @dragover.prevent
    @drop="onDrop"
  >
    <VCard
      v-for="(field, index) in fields"
      :key="index"
      class="mb-2 pa-2"
    >
      <div class="d-flex justify-space-between align-center">
        <span>{{ field.label }}</span>
        <VBtn
          size="small"
          icon
          @click="edit(index)"
        >
          ✏️
        </VBtn>
      </div>

      <!-- ✅ Add this block below the label + edit button -->
      <template v-if="['select', 'radio'].includes(field.type) && field.options?.length">
        <div class="text-caption text-grey mt-2">
          Options:
          <ul class="ps-4">
            <li
              v-for="opt in field.options"
              :key="opt"
              class="text-grey-darken-1"
            >
              {{ opt }}
            </li>
          </ul>
        </div>
      </template>
    </VCard>
  </div>
</template>

<style scoped lang="scss">
//
</style>
