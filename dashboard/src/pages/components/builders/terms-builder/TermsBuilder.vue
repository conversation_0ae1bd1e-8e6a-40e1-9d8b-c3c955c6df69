<script setup>
import { ref } from 'vue'
import TermsBuilderPalette from './TermsBuilderPalette.vue'
import TermsBuilderCanvas from './TermsBuilderCanvas.vue'
import TermsFieldEditor from './TermsFieldEditor.vue'
import { exportTermsSchema, renderTermsSchema } from './termsUtils'

const props = defineProps({
  title: {
    type: String,
    default: 'Terms and Conditions',
  },
})

const emit = defineEmits(['update:schema'])

const renderMode = ref(false)
const parsedSchema = ref({})
const termsTitle = ref(props.title)

function toggleRender() {
  if (!schema.value) return
  parsedSchema.value = JSON.parse(schema.value)
  renderMode.value = !renderMode.value
}

const builder = ref(null)
const schema = ref('')
const fieldToEdit = ref(null)
const editIndex = ref(-1)

function editField(index, field) {
  editIndex.value = index
  fieldToEdit.value = field
}

function updateField(index, updatedField) {
  builder.value.fields[index] = updatedField
  fieldToEdit.value = null
  editIndex.value = -1
}

function exportJson() {
  schema.value = JSON.stringify(
    exportTermsSchema(builder.value.fields, termsTitle.value),
    null,
    2,
  )
  emit('update:schema', schema.value)
}
</script>

<template>
  <VRow>
    <VCol>
      <VTextField
        v-model="termsTitle"
        label="Terms Title"
        class="mb-4"
      />
    </VCol>
  </VRow>
  <VRow>
    <VCol cols="3">
      <TermsBuilderPalette />
    </VCol>

    <VCol cols="6">
      <TermsBuilderCanvas
        ref="builder"
        @edit="editField"
      />
    </VCol>

    <VCol cols="3">
      <VBtn
        block
        @click="exportJson"
      >
        Save Terms
      </VBtn>
      <pre
        v-if="schema"
        class="mt-4"
        style="font-size: 0.75rem; max-height: 300px; overflow: auto;"
      >{{ schema }}</pre>

      <VBtn
        v-if="schema"
        class="mt-2"
        block
        @click="toggleRender"
      >
        {{ renderMode ? 'Back to Editor' : 'Preview Terms' }}
      </VBtn>
    </VCol>
  </VRow>

  <VRow v-if="renderMode">
    <VCol>
      <VCard :title="parsedSchema.title || 'Terms and Conditions'">
        <VCardText>
          <div
            v-for="(field, index) in parsedSchema.fields"
            :key="index"
            class="mb-4"
          >
            <template v-if="field.type === 'heading'">
              <h3 class="text-h5">
                {{ field.content }}
              </h3>
            </template>
            
            <template v-else-if="field.type === 'paragraph'">
              <p class="text-body-1">
                {{ field.content }}
              </p>
            </template>
            
            <template v-else-if="field.type === 'bullet-list'">
              <ul class="ms-4">
                <li
                  v-for="(item, i) in field.items"
                  :key="i"
                >
                  {{ item }}
                </li>
              </ul>
            </template>
            
            <template v-else-if="field.type === 'numbered-list'">
              <ol class="ms-4">
                <li
                  v-for="(item, i) in field.items"
                  :key="i"
                >
                  {{ item }}
                </li>
              </ol>
            </template>
            
            <template v-else-if="field.type === 'section'">
              <div class="pa-2 border rounded">
                <h4 class="text-h6">
                  {{ field.label }}
                </h4>
                <p>{{ field.content }}</p>
              </div>
            </template>
            
            <template v-else-if="field.type === 'clause'">
              <div class="pa-2 border rounded">
                <h4 class="text-h6">
                  {{ field.label }}
                </h4>
                <p>{{ field.content }}</p>
              </div>
            </template>
            
            <template v-else-if="field.type === 'signature'">
              <VTextField
                label="Signature"
                readonly
              />
            </template>
            
            <template v-else-if="field.type === 'date'">
              <VTextField
                label="Date"
                readonly
              />
            </template>
          </div>
        </VCardText>
      </VCard>
    </VCol>
  </VRow>

  <TermsFieldEditor
    :model-value="fieldToEdit"
    :index="editIndex"
    @update="updateField"
  />
</template>

<style scoped lang="scss">
//
</style>
