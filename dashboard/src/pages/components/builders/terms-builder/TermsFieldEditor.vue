<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: [Array, Object, String, Number],
  },
  index: {
    type: [Number, String],
  },
})

const emit = defineEmits(['update'])

const visible = ref(false)
const field = ref({})
const items = ref('')

// Watch for incoming field and initialize editor
watch(
  () => props.modelValue,
  newField => {
    if (!newField) return

    // Create a deep copy
    field.value = JSON.parse(JSON.stringify(newField))

    // Initialize comma-separated items string for lists
    if (['bullet-list', 'numbered-list'].includes(field.value.type)) {
      items.value = (field.value.items || []).join('\n')
    } else {
      items.value = ''
    }

    visible.value = true
  },
  { immediate: true },
)

// Sync text input `items` with `field.items`
watch(items, val => {
  if (['bullet-list', 'numbered-list'].includes(field.value.type)) {
    field.value.items = val
      .split('\n')
      .map(item => item.trim())
      .filter(Boolean)
  }
})

// Handle save
function save() {
  emit('update', props.index, field.value)
  visible.value = false
}
</script>

<template>
  <VDialog
    v-model="visible"
    width="500"
  >
    <VCard>
      <VCardTitle>Edit {{ field.type ? field.type.replace('-', ' ') : '' }}</VCardTitle>

      <VCardText>
        <VTextField
          v-model="field.label"
          label="Label"
        />

        <!-- Content for text-based fields -->
        <template v-if="['heading', 'paragraph', 'section', 'clause'].includes(field.type)">
          <VTextarea
            v-model="field.content"
            label="Content"
            rows="5"
            auto-grow
          />
        </template>

        <!-- Items for list fields -->
        <template v-if="['bullet-list', 'numbered-list'].includes(field.type)">
          <VTextarea
            v-model="items"
            label="List Items (one per line)"
            rows="5"
            auto-grow
            hint="Enter each list item on a new line"
            persistent-hint
          />
        </template>

        <!-- Required flag for signature and date fields -->
        <template v-if="['signature', 'date'].includes(field.type)">
          <VSwitch
            v-model="field.required"
            label="Required"
          />
        </template>
      </VCardText>

      <VCardActions>
        <VSpacer />
        <VBtn
          text
          @click="visible = false"
        >
          Cancel
        </VBtn>
        <VBtn @click="save">
          Save
        </VBtn>
      </VCardActions>
    </VCard>
  </VDialog>
</template>

<style scoped lang="scss">
//
</style>
