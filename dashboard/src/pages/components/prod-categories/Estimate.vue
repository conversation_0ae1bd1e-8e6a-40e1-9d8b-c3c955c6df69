<script setup>
import Ligs from '@/pages/components/prod-categories/Ligs.vue'
import { useSocketStore } from '@stores/auth'
import { useSocketCategoryStore } from '@stores/categories'
import { storeToRefs } from 'pinia'
import { socket } from '@socket/socket'
import { themeConfig } from '@themeConfig'
import { VNodeRenderer } from '@layouts/components/VNodeRenderer'
import { VueDraggableNext } from 'vue-draggable-next'
import { v4 as uuidv4 } from 'uuid'

const props = defineProps({
  groupedItems: {
    type: [Array, Object],
    required: true,
  },
})

const emit = defineEmits([
  'groupedItems',
])

const store = useSocketStore()
const storeCategory = useSocketCategoryStore()

const { user } = storeToRefs(store)
const { categories } = storeToRefs(storeCategory)

socket.on('categoriesAll', data => {
  categories.value = data.data
})

onMounted(() => {
  storeCategory.getCategories({
    user: user.value._id,
  })
})

let lineItems = ref([])
let isAdvancedOptions = ref(null)

/*const groupedItems = ref({
  groups: [],
})*/

const open = ref([])

const addLineItem = item => {
  if (!props.groupedItems[0]) {
    // eslint-disable-next-line vue/no-mutating-props
    props.groupedItems.push({
      name: item.categoryId.name,
      items: [{
        uuid: uuidv4(),
        lineItemName: item.name,
        name: '',
        qty: '',
        time: '',
        timeDuration: '',
        rate: '',
        markUp: '',
        privateMemo: '',
        baseHours: '',
        oTNormalHours: '',
        oTSpecialHours: '',
        memo: '',
        hasAdvanced: false,
        taxable: false,
        pensionWelfare: false,
        type: false,
      }],
    })
    console.log('no array', props.groupedItems)
  } else {
    const found = props.groupedItems.some(group => group.name === item.categoryId.name)

    console.log({ found })
    if(found) {
      const result = props.groupedItems.filter(group => group.name === item.categoryId.name)

      console.log(result)

      result[0].items.push({
        uuid: uuidv4(),
        lineItemName: item.name,
        name: '',
        qty: '',
        time: '',
        timeDuration: '',
        rate: '',
        markUp: '',
        privateMemo: '',
        baseHours: '',
        oTNormalHours: '',
        oTSpecialHours: '',
        memo: '',
        hasAdvanced: false,
        taxable: false,
        pensionWelfare: false,
        type: false,
      })
    } else {
      // eslint-disable-next-line vue/no-mutating-props
      props.groupedItems.push({
        name: item.categoryId.name,
        items: [{
          uuid: uuidv4(),
          lineItemName: item.name,
          name: '',
          qty: '',
          time: '',
          timeDuration: '',
          rate: '',
          markUp: '',
          privateMemo: '',
          baseHours: '',
          oTNormalHours: '',
          oTSpecialHours: '',
          memo: '',
          hasAdvanced: false,
          taxable: false,
          pensionWelfare: false,
          type: false,
        }],
      })
    }
    console.log('yes array')
  }

  console.log(props.groupedItems)
}

const removeGroup = id => {
  // eslint-disable-next-line vue/no-mutating-props
  props.groupedItems.splice(id, 1)
  console.log(props.groupedItems)
}

const removeLineItem = (uuid, groupName) => {
  const group = props.groupedItems.find(group => group.name === groupName)
  if (!group) return

  const index = group.items.findIndex(item => item.uuid === uuid)
  if (index !== -1) {
    group.items.splice(index, 1)
  }
}

const showAdvanced = idx => {
  isAdvancedOptions.value === idx ? (isAdvancedOptions.value = null) : (isAdvancedOptions.value = idx)
}
</script>

<template>
  <div>
    <VCard
      flat
      variant="outlined"
      class="mb-5"
    >
      <VCardText>
        <VRow>
          <VCol>
            <VBtn variant="text">
              <VIcon
                icon="grommet-icons:select"
                class="me-2"
              /> Select / Deselect All
            </VBtn>
          </VCol>
          <VCol>
            <VBtn variant="text">
              <VIcon
                icon="subway:print"
                class="me-2"
              /> Print Estimate
            </VBtn>
          </VCol>
          <VCol>
            <VBtn variant="text">
              <VIcon
                icon="mingcute:mail-send-line"
                class="me-2"
              /> Email Estimate
            </VBtn>
          </VCol>
          <VCol>
            <VBtn variant="text">
              <VIcon
                icon="tabler:share"
                class="me-2"
              /> Share Job
            </VBtn>
          </VCol>
        </VRow>
      </VCardText>
    </VCard>
    <VRow>
      <VCol
        cols="12"
        lg="9"
      >
        <VContainer v-if="!groupedItems[0]">
          <VRow class="pt-10 pb-10">
            <VCol>
              <h1 class="custom-font text-primary">
                Add your estimate items from the category list
              </h1>
            </VCol>
            <VCol>
              <VNodeRenderer :nodes="themeConfig.app.arrowSVG" />
            </VCol>
          </VRow>
        </VContainer>
        <!--          </VAlertTitle> -->
        <!--        </VAlert> -->
        <VueDraggableNext
          v-model="groupedItems.groups"
          ghost-class="on-drag"
          class="flex flex-col justify-center list-group"
          tag="ul"
          :group="{ name: 'groups', put: false }"
          item-key="name"
        >
          <VCard
            v-for="(item, index) in groupedItems"
            :key="item.id"
            class="mb-5 specificpad"
            variant="outlined"
            :title="item.name"
          >
            <template #append>
              <IconBtn @click="removeGroup(index)">
                <VIcon
                  size="20"
                  icon="tabler-x"
                />
              </IconBtn>
            </template>
            <VExpansionPanels multiple>
              <VueDraggableNext
                v-model="item.items"
                ghost-class="on-drag"
                class="list-group subItem"
                tag="v-card"
                :group="{ name: 'item', put: false }"
                item-key="name"
              >
                <VExpansionPanel
                  v-for="i in item.items"
                  :key="i.id"
                  class="mt-5 mb-5"
                >
                  <VExpansionPanelTitle>
                    {{ i.lineItemName }}
                  </VExpansionPanelTitle>
                  <VExpansionPanelText>
                    <VRow>
                      <VCol cols="10" />
                      <VCol cols="1">
                        <IconBtn @click="removeLineItem(i.uuid, item.name)">
                          <VIcon
                            size="20"
                            icon="tabler-x"
                          />
                        </IconBtn>
                      </VCol>
                      <VCol cols="1">
                        <IconBtn @click="showAdvanced(i.uuid)">
                          <VIcon
                            size="20"
                            icon="tabler-settings"
                          />
                          <VTooltip
                            activator="parent"
                            location="end"
                          >
                            Advanced Options
                          </VTooltip>
                        </IconBtn>
                      </VCol>
                    </VRow>
                    <VRow>
                      <VCol cols="12">
                        <VTextField
                          v-model="i.name"
                          label="Name"
                        />
                      </VCol>
                      <VCol
                        cols="6"
                        lg="3"
                      >
                        <VTextField
                          v-model="i.qty"
                          label="QTY"
                        />
                      </VCol>
                      <VCol
                        cols="6"
                        lg="3"
                      >
                        <VTextField
                          v-model="i.time"
                          label="Time"
                        />
                      </VCol>
                      <VCol
                        cols="6"
                        lg="3"
                      >
                        <VTextField
                          v-model="i.rate"
                          label="Rate"
                        />
                      </VCol>
                      <VCol
                        cols="6"
                        lg="3"
                      >
                        <VTextField
                          v-model="i.markUp"
                          label="Markup"
                        />
                      </VCol>
                      <VCol
                        v-show="isAdvancedOptions === i.uuid"
                        cols="12"
                      >
                        <VTextarea
                          v-model="i.privateMemo"
                          label="Private Memo"
                        />
                      </VCol>
                      <VCol
                        v-show="isAdvancedOptions === i.uuid"
                        cols="12"
                        lg="4"
                      >
                        <VTextField
                          v-model="i.baseHours"
                          label="Base Hours"
                        />
                      </VCol>
                      <VCol
                        v-show="isAdvancedOptions === i.uuid"
                        cols="12"
                        lg="4"
                      >
                        <VTextField
                          v-model="i.oTNormalHours"
                          label="1.5x (Over Time)"
                        />
                      </VCol>
                      <VCol
                        v-show="isAdvancedOptions === i.uuid"
                        cols="12"
                        lg="4"
                      >
                        <VTextField
                          v-model="i.oTSpecialHours"
                          label="2x (Over Time)"
                        />
                      </VCol>
                      <VCol
                        v-show="isAdvancedOptions === i.uuid"
                        cols="12"
                        lg="4"
                      >
                        <VCheckbox
                          v-model="i.taxable"
                          label="Taxable"
                        />
                      </VCol>
                      <VCol
                        v-show="isAdvancedOptions === i.uuid"
                        cols="12"
                        lg="4"
                      >
                        <VCheckbox
                          v-model="i.pensionWelfare"
                          label="P & W (%)"
                        >
                          <VTooltip
                            activator="parent"
                            location="top"
                          >
                            Pension & Welfare
                          </VTooltip>
                        </VCheckbox>
                      </VCol>
                      <VCol
                        v-show="isAdvancedOptions === i.uuid"
                        cols="12"
                        lg="4"
                      >
                        <VCheckbox
                          v-model="i.healthSafety"
                          label="Health & Safety"
                        />
                      </VCol>
                      <VCol cols="12">
                        <VTextarea
                          v-model="item.memo"
                          label="Memo"
                        />
                      </VCol>
                    </VRow>
                  </VExpansionPanelText>
                </VExpansionPanel>
              </VueDraggableNext>
            </VExpansionPanels>
          </VCard>
        </VueDraggableNext>
      </VCol>
      <VCol
        cols="12"
        lg="3"
      >
        <VCard variant="outlined">
          <VCardText v-if="categories">
            <Ligs
              :ligs="lineItems"
              :open="open"
              :categories="categories"
              @add-line-item="addLineItem"
            />
          </VCardText>
        </VCard>
      </VCol>
    </VRow>
  </div>
</template>

<style scoped lang="scss">
@import url(https://fonts.googleapis.com/css?family=Architects+Daughter);
.custom-font {
  font-family: 'Architects Daughter', sans-serif;
}
.dragArea {
  min-height: 50px;
  outline: 1px dashed;
}
.dragAreaSub {
  min-height: 50px;
  outline: 1px dashed;
  width: 100%
}
.subItem {
  width: 95%
}
.specificpad {
  padding: 15px
}
</style>
