<script setup>
import { useSocketStore } from '@stores/auth'
import { useSocketJobStore } from '@stores/jobs'
import { useSocketQuoteStore } from '@stores/quotes'
import InvoiceEditable from './components/invoice/InvoiceEditable.vue'
import InvoiceSendInvoiceDrawer from './components/invoice/InvoiceSendInvoiceDrawer.vue'
import { useHead } from '@unhead/vue'
import { socket } from '@socket/socket'
import { toast } from 'vue3-toastify'
import 'vue3-toastify/dist/index.css'
import { useTheme } from 'vuetify'

const store = useSocketStore()
const storeJobs = useSocketJobStore()
const storeQuote = useSocketQuoteStore()

const vuetifyTheme = useTheme()
const route = useRoute()

const { user } = storeToRefs(store)
const { job } = storeToRefs(storeJobs)
const { quotes, quote } = storeToRefs(storeQuote)

definePage({
  meta: {
    layout: 'default',
  },
})

useHead({
  title: 'Qwote Z | Quotation',
  meta: [
    {
      name: 'description',
      content: 'My page description',
    },
  ],
})

socket.on('updateQuote', data => {
  switch (data.status) {
  case 'success':
    toast.success(data.message)
    quote.value = data.data

    // router.push({ name: 'admin-dashboard' })
    break
  case 'error':
    toast.success(data.message)
    break
  default:
    break
  }
})

const isSendSidebarActive = ref(false)
const paymentTerms = ref(true)
const clientNotes = ref(false)
const paymentStub = ref(false)
const isAddPaymentSidebarActive = ref(false)
const selectedPaymentMethod = ref('Bank Account')

const paymentMethods = [
  'Bank Account',
  'PayPal',
  'Card Payment',
]

/*onMounted(() => {
  quote.value.quoteNumber = user.value.profile.defaultIdentity.name
})*/

const addProduct = value => {
  quote.value?.items.push(value)
}

const removeProduct = id => {
  quote.value?.items.splice(id, 1)
}

const submitQuote = () => {
  console.log('yesdr')
  isSendSidebarActive.value = true
  storeQuote.updateQuote({
    id: quote.value.id,
    user: user.value._id,
    data: quote.value,
  })
}

const saveQuote = () => {
  console.log('yes update')
  storeQuote.updateQuote({
    id: quote.value.id,
    user: user.value._id,
    data: quote.value,
  })
}
</script>

<template>
  <VRow>
    <!-- 👉 InvoiceEditable -->
    <VCol
      cols="12"
      md="9"
    >
      <InvoiceEditable
        :data="quote"
        type="Quotation"
        @push="addProduct"
        @remove="removeProduct"
      />
    </VCol>

    <!-- 👉 Right Column: Invoice Action -->
    <VCol
      cols="12"
      md="3"
    >
      <VCard class="mb-8">
        <VCardText>
          <!-- 👉 Send Invoice -->
          <VBtn
            block
            prepend-icon="tabler-send"
            class="mb-2"
            @click="submitQuote"
          >
            Send Quotation
          </VBtn>

          <!-- 👉 Preview -->
          <VBtn
            block
            color="default"
            variant="tonal"
            class="mb-2"
          >
            <!--            :to="{ name: 'apps-invoice-preview-id', params: { id: '5036' } }" -->
            Preview
          </VBtn>

          <!-- 👉 Save -->
          <VBtn
            block
            color="default"
            variant="tonal"
            @click="saveQuote"
          >
            Save
          </VBtn>
        </VCardText>
      </VCard>

      <!-- 👉 Select payment method -->
      <AppSelect
        v-model="selectedPaymentMethod"
        :items="paymentMethods"
        label="Accept Payment Via"
        class="mb-6"
      />

      <!-- 👉 Payment Terms -->
      <div class="d-flex align-center justify-space-between mb-2">
        <VLabel for="payment-terms">
          Payment Terms
        </VLabel>
        <div>
          <VSwitch
            id="payment-terms"
            v-model="paymentTerms"
          />
        </div>
      </div>

      <!-- 👉  Client Notes -->
      <div class="d-flex align-center justify-space-between mb-2">
        <VLabel for="client-notes">
          Client Notes
        </VLabel>
        <div>
          <VSwitch
            id="client-notes"
            v-model="clientNotes"
          />
        </div>
      </div>

      <!-- 👉  Payment Stub -->
      <div class="d-flex align-center justify-space-between">
        <VLabel for="payment-stub">
          Payment Stub
        </VLabel>
        <div>
          <VSwitch
            id="payment-stub"
            v-model="paymentStub"
          />
        </div>
      </div>
    </VCol>

    <!-- 👉 Invoice send drawer -->
    <InvoiceSendInvoiceDrawer
      :id="quote.id"
      v-model:is-drawer-open="isSendSidebarActive"
      :email="quote.emailedTo"
      :status="quote.status"
    />
  </VRow>
</template>


