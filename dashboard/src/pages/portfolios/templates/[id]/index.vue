<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { storeToRefs } from 'pinia'
import { useSocketStore } from '@stores/auth'
import { useSocketPortfolioTemplateStore } from '@stores/portfolios/templates'
import { socket } from '@socket/socket'
import { toast } from 'vue3-toastify'
import 'vue3-toastify/dist/index.css'
import { useTheme } from 'vuetify'
import { useHead } from '@unhead/vue'

const storeAuth = useSocketStore()
const templateStore = useSocketPortfolioTemplateStore()

const { user } = storeToRefs(storeAuth)
const { template } = storeToRefs(templateStore)

const vuetifyTheme = useTheme()
const router = useRouter()
const route = useRoute()

const templateId = computed(() => route.params.id)

definePage({
  meta: {
    layout: 'default',
  },
})

useHead({
  title: 'View Portfolio Template | Qwote Z',
  meta: [
    {
      name: 'description',
      content: 'View portfolio template details',
    },
  ],
})

// State
const loading = ref(false)

// Socket event listeners
socket.on('singlePortfolioTemplate', data => {
  loading.value = false
  if (data.status === 'success') {
    template.value = data.data
  } else {
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
  }
})

socket.on('clonePortfolioTemplate', data => {
  if (data.status === 'success') {
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })

    // Navigate to portfolio creation with template data
    /*navigateTo('/portfolios/create', {
      query: { template: data.data.template._id },
    })*/
    router.push({ name: 'portfolios-create', query: { template: data.data.template._id } })
  } else {
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
  }
})

// Load template on mount
onMounted(() => {
  if (templateId.value) {
    loading.value = true
    templateStore.getPortfolioTemplate({
      user: user.value._id,
      id: templateId.value,
    })
  }
})

// Methods
function handleEdit() {
  router.push({ name: 'portfolios-templates-id-edit', params: { id: templateId.value } })
}

function handleBack() {
  router.push({ name: 'portfolios-templates' })
}

function handleClone() {
  templateStore.clonePortfolioTemplate({
    user: user.value._id,
    templateId: templateId.value,
    portfolioTitle: `${template.value.name} Portfolio`,
    portfolioDescription: template.value.description,
  })
}

function formatDate(date) {
  return new Date(date).toLocaleDateString()
}

function getCategoryColor(category) {
  const colors = {
    photography: 'purple',
    videography: 'red',
    design: 'blue',
    architecture: 'green',
    fashion: 'pink',
    art: 'orange',
    business: 'indigo',
    personal: 'teal',
    agency: 'cyan',
    freelancer: 'amber',
    other: 'grey',
  }

  
  return colors[category] || 'grey'
}

function getCategoryIcon(category) {
  const icons = {
    photography: 'tabler-camera',
    videography: 'tabler-video',
    design: 'tabler-palette',
    architecture: 'tabler-building',
    fashion: 'tabler-shirt',
    art: 'tabler-brush',
    business: 'tabler-briefcase',
    personal: 'tabler-user',
    agency: 'tabler-users',
    freelancer: 'tabler-user-star',
    other: 'tabler-category',
  }

  
  return icons[category] || 'tabler-category'
}
</script>

<template>
  <div>
    <!-- Header -->
    <div class="d-flex align-center justify-space-between mb-6">
      <div>
        <h1 class="text-h4 font-weight-bold mb-2">
          Template Details
        </h1>
        <p class="text-body-1 text-medium-emphasis">
          View template information and components
        </p>
      </div>
      <div class="d-flex gap-2">
        <VBtn
          color="secondary"
          variant="outlined"
          prepend-icon="tabler-arrow-left"
          @click="handleBack"
        >
          Back to Templates
        </VBtn>
        <VBtn
          color="success"
          variant="outlined"
          prepend-icon="tabler-copy"
          @click="handleClone"
        >
          Clone Template
        </VBtn>
        <VBtn
          color="primary"
          prepend-icon="tabler-edit"
          @click="handleEdit"
        >
          Edit
        </VBtn>
      </div>
    </div>

    <!-- Loading State -->
    <div
      v-if="loading"
      class="text-center py-12"
    >
      <VProgressCircular
        indeterminate
        color="primary"
        size="48"
      />
      <div class="mt-4 text-body-1">
        Loading template...
      </div>
    </div>

    <!-- Template Details -->
    <div v-else-if="template">
      <VRow>
        <VCol
          cols="12"
          md="8"
        >
          <VCard>
            <VCardTitle class="d-flex align-center justify-space-between">
              <div class="d-flex align-center">
                <VIcon
                  :icon="getCategoryIcon(template.category)"
                  class="me-2"
                  :color="getCategoryColor(template.category)"
                />
                <span>{{ template.name }}</span>
              </div>
              
              <div class="d-flex gap-1">
                <VChip
                  v-if="template.isPremium"
                  color="warning"
                  size="small"
                  variant="tonal"
                >
                  Premium
                </VChip>
                <VChip
                  v-if="template.isPublic"
                  color="success"
                  size="small"
                  variant="tonal"
                >
                  Public
                </VChip>
              </div>
            </VCardTitle>

            <VCardText>
              <div class="mb-4">
                <h3 class="text-h6 mb-2">
                  Description
                </h3>
                <p class="text-body-1">
                  {{ template.description || 'No description provided' }}
                </p>
              </div>

              <!-- Category and Tags -->
              <div class="mb-4">
                <h3 class="text-h6 mb-2">
                  Category & Tags
                </h3>
                <div class="d-flex align-center gap-2 mb-2">
                  <VChip
                    :color="getCategoryColor(template.category)"
                    variant="tonal"
                  >
                    {{ template.category }}
                  </VChip>
                </div>
                
                <div
                  v-if="template.tags && template.tags.length > 0"
                  class="d-flex flex-wrap gap-2"
                >
                  <VChip
                    v-for="tag in template.tags"
                    :key="tag"
                    size="small"
                    variant="outlined"
                  >
                    {{ tag }}
                  </VChip>
                </div>
              </div>

              <!-- Components Section -->
              <VDivider class="my-4" />
              
              <div>
                <h3 class="text-h6 mb-3">
                  Components
                </h3>
                
                <div v-if="template.components && template.components.length > 0">
                  <VCard
                    v-for="(component, index) in template.components"
                    :key="component._id"
                    variant="outlined"
                    class="mb-3"
                  >
                    <VCardText>
                      <div class="d-flex align-center justify-space-between">
                        <div class="d-flex align-center">
                          <VChip
                            size="small"
                            color="primary"
                            variant="tonal"
                            class="me-3"
                          >
                            {{ index + 1 }}
                          </VChip>
                          <div>
                            <div class="text-body-1 font-weight-medium">
                              {{ component.type || `Component ${index + 1}` }}
                            </div>
                            <div class="text-caption text-medium-emphasis">
                              {{ component.layout?.length || 0 }} layout elements
                            </div>
                          </div>
                        </div>
                        
                        <VChip
                          size="small"
                          variant="tonal"
                          color="info"
                        >
                          Order: {{ component.order || index + 1 }}
                        </VChip>
                      </div>
                    </VCardText>
                  </VCard>
                </div>
                
                <div
                  v-else
                  class="text-center py-8"
                >
                  <VIcon
                    icon="tabler-components"
                    size="48"
                    class="text-medium-emphasis mb-3"
                  />
                  <p class="text-body-2 text-medium-emphasis">
                    No components added yet
                  </p>
                </div>
              </div>
            </VCardText>
          </VCard>
        </VCol>

        <VCol
          cols="12"
          md="4"
        >
          <VCard>
            <VCardTitle>Template Information</VCardTitle>
            <VCardText>
              <div class="space-y-4">
                <div>
                  <div class="text-caption text-medium-emphasis mb-1">
                    Components
                  </div>
                  <div class="text-body-2">
                    {{ template.components?.length || 0 }} components
                  </div>
                </div>
                
                <div>
                  <div class="text-caption text-medium-emphasis mb-1">
                    Usage Count
                  </div>
                  <div class="text-body-2">
                    {{ template.usageCount || 0 }} times used
                  </div>
                </div>
                
                <div v-if="template.rating && template.rating.count > 0">
                  <div class="text-caption text-medium-emphasis mb-1">
                    Rating
                  </div>
                  <div class="d-flex align-center gap-2">
                    <VRating
                      :model-value="template.rating.average"
                      readonly
                      size="small"
                      density="compact"
                    />
                    <span class="text-body-2">({{ template.rating.count }})</span>
                  </div>
                </div>
                
                <div>
                  <div class="text-caption text-medium-emphasis mb-1">
                    Created
                  </div>
                  <div class="text-body-2">
                    {{ formatDate(template.createdAt) }}
                  </div>
                </div>
                
                <div>
                  <div class="text-caption text-medium-emphasis mb-1">
                    Created By
                  </div>
                  <div class="text-body-2">
                    {{ template.createdBy?.name || 'Unknown' }}
                  </div>
                </div>
                
                <div>
                  <div class="text-caption text-medium-emphasis mb-1">
                    Status
                  </div>
                  <div class="d-flex gap-1">
                    <VChip
                      v-if="template.isPublic"
                      color="success"
                      size="small"
                      variant="tonal"
                    >
                      Public
                    </VChip>
                    <VChip
                      v-if="template.isPremium"
                      color="warning"
                      size="small"
                      variant="tonal"
                    >
                      Premium
                    </VChip>
                    <VChip
                      v-if="!template.isPublic && !template.isPremium"
                      color="default"
                      size="small"
                      variant="tonal"
                    >
                      Private
                    </VChip>
                  </div>
                </div>
              </div>
            </VCardText>
          </VCard>
        </VCol>
      </VRow>
    </div>

    <!-- Error State -->
    <div
      v-else
      class="text-center py-12"
    >
      <VIcon
        icon="tabler-alert-circle"
        size="64"
        class="text-error mb-4"
      />
      <h3 class="text-h6 mb-2">
        Template Not Found
      </h3>
      <p class="text-body-2 text-medium-emphasis mb-4">
        The template you're looking for doesn't exist or you don't have access to it.
      </p>
      <VBtn
        color="primary"
        @click="handleBack"
      >
        Back to Templates
      </VBtn>
    </div>
  </div>
</template>
