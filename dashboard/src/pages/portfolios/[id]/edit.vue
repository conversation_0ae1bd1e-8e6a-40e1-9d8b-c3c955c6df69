<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { storeToRefs } from 'pinia'
import { useSocketStore } from '@stores/auth'
import { useSocketPortfolioStore } from '@stores/portfolios'
import { socket } from '@socket/socket'
import { toast } from 'vue3-toastify'
import 'vue3-toastify/dist/index.css'
import { useTheme } from 'vuetify'
import { useHead } from '@unhead/vue'

const route = useRoute()
const storeAuth = useSocketStore()
const portfolioStore = useSocketPortfolioStore()

const { user } = storeToRefs(storeAuth)
const { portfolio } = storeToRefs(portfolioStore)

const vuetifyTheme = useTheme()
const router = useRouter()

const portfolioId = computed(() => route.params.id)

definePage({
  meta: {
    layout: 'default',
  },
})

useHead({
  title: 'Edit Portfolio | Qwote Z',
  meta: [
    {
      name: 'description',
      content: 'Edit portfolio',
    },
  ],
})

// State
const loading = ref(false)
const saving = ref(false)

const portfolioForm = ref({
  title: '',
  description: '',
  published: false,
})

// Socket event listeners
socket.on('singlePortfolio', data => {
  loading.value = false
  if (data.status === 'success') {
    portfolio.value = data.data
    portfolioForm.value = {
      title: data.data.title,
      description: data.data.description || '',
      published: data.data.published,
    }
  } else {
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
  }
})

socket.on('updatePortfolio', data => {
  saving.value = false
  if (data.status === 'success') {
    portfolio.value = data.data.portfolio
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })
  } else {
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'error',
    })
  }
})

// Load portfolio on mount
onMounted(() => {
  if (portfolioId.value) {
    loading.value = true
    portfolioStore.getPortfolio({
      user: user.value._id,
      id: portfolioId.value,
    })
  }
})

// Methods
function handleSave() {
  console.log('🔥 handleSave() called - Saving portfolio')
  if (!portfolioForm.value.title.trim()) {
    toast('Title is required', {
      autoClose: 3000,
      type: 'error',
    })

    return
  }

  saving.value = true
  portfolioStore.updatePortfolio({
    user: user.value._id,
    id: portfolioId.value,
    title: portfolioForm.value.title,
    description: portfolioForm.value.description,
    published: portfolioForm.value.published,
  })
}

function handleCancel() {
  console.log('🚪 handleCancel() called - Navigating to portfolios')
  router.push({ name: 'portfolios' })
}

function handleOpenBuilder() {
  console.log('🔧 handleOpenBuilder() called - Navigating to page builder with ID:', portfolioId.value)
  router.push({ name: 'portfolios-id-page-builder', params: { id: portfolioId.value } })
}

// Form validation
const isFormValid = computed(() => {
  return portfolioForm.value.title.trim() !== ''
})
</script>

<template>
  <div>
    <!-- Header -->
    <div class="d-flex align-center justify-space-between mb-6">
      <div>
        <h1 class="text-h4 font-weight-bold mb-2">
          Edit Portfolio
        </h1>
        <p class="text-body-1 text-medium-emphasis">
          Update portfolio details and settings
        </p>
      </div>
      <div class="d-flex gap-3 align-center">
        <VBtn
          color="secondary"
          variant="outlined"
          prepend-icon="tabler-arrow-left"
          @click.prevent="handleCancel"
        >
          Cancel
        </VBtn>
        <VBtn
          color="info"
          variant="outlined"
          prepend-icon="tabler-settings"
          @click.prevent="handleOpenBuilder"
        >
          Page Builder
        </VBtn>
        <VBtn
          color="primary"
          :loading="saving"
          :disabled="!isFormValid"
          prepend-icon="tabler-device-floppy"
          @click.prevent="handleSave"
        >
          <template #prepend>
            <VIcon
              v-if="!saving"
              icon="tabler-device-floppy"
            />
          </template>
          Save Changes
        </VBtn>
      </div>
    </div>

    <!-- Loading State -->
    <div
      v-if="loading"
      class="text-center py-12"
    >
      <VProgressCircular
        indeterminate
        color="primary"
        size="48"
      />
      <div class="mt-4 text-body-1">
        Loading portfolio...
      </div>
    </div>

    <!-- Edit Form -->
    <VCard v-else>
      <VCardText>
        <VForm @submit.prevent="handleSave">
          <VRow>
            <VCol
              cols="12"
              md="8"
            >
              <VRow>
                <VCol cols="12">
                  <AppTextField
                    v-model="portfolioForm.title"
                    label="Portfolio Title"
                    placeholder="Enter a descriptive title"
                    :rules="[v => !!v || 'Title is required']"
                  />
                </VCol>

                <VCol cols="12">
                  <AppTextarea
                    v-model="portfolioForm.description"
                    label="Description (Optional)"
                    placeholder="Describe your portfolio"
                    rows="4"
                  />
                </VCol>

                <VCol cols="12">
                  <VCheckbox
                    v-model="portfolioForm.published"
                    label="Published"
                    density="compact"
                  />
                  <div class="text-caption text-medium-emphasis mt-1">
                    Published portfolios are visible to the public
                  </div>
                </VCol>
              </VRow>
            </VCol>

            <VCol
              cols="12"
              md="4"
            >
              <VCard variant="outlined">
                <VCardTitle>Portfolio Info</VCardTitle>
                <VCardText>
                  <div
                    v-if="portfolio"
                    class="space-y-3"
                  >
                    <div>
                      <div class="text-caption text-medium-emphasis">
                        URL Slug
                      </div>
                      <div class="text-body-2">
                        {{ portfolio.slug || 'Not generated' }}
                      </div>
                    </div>
                    
                    <div>
                      <div class="text-caption text-medium-emphasis">
                        Components
                      </div>
                      <div class="text-body-2">
                        {{ portfolio.components?.length || 0 }} components
                      </div>
                    </div>
                    
                    <div>
                      <div class="text-caption text-medium-emphasis">
                        Created
                      </div>
                      <div class="text-body-2">
                        {{ new Date(portfolio.createdAt).toLocaleDateString() }}
                      </div>
                    </div>
                    
                    <div>
                      <div class="text-caption text-medium-emphasis">
                        Status
                      </div>
                      <VChip
                        :color="portfolio.published ? 'success' : 'warning'"
                        size="small"
                        variant="tonal"
                      >
                        {{ portfolio.published ? 'Published' : 'Draft' }}
                      </VChip>
                    </div>
                  </div>
                </VCardText>
              </VCard>
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
    </VCard>
  </div>
</template>
