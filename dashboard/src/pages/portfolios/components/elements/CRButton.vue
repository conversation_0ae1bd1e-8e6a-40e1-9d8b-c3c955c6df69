<script setup lang="ts">
import { defineProps } from 'vue'

const props = defineProps({
  color: {
    type: String,
    default: 'primary', // Default color is primary if not provided
  },
  text: {
    type: String,
    default: 'Click Me', // Default text if not provided
  },
  align: {
    type: String,
    default: 'text-center', // Default text if not provided
  },
})
</script>

<template>
  <div :class="{ [props.align]: true}">
    <VBtn :color="props.color">
      {{ props.text }}
    </VBtn>
  </div>
</template>

<style scoped lang="scss">
// Add your styles here if necessary
</style>
