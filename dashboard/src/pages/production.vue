<script setup>
import { useSocketStore } from '@stores/auth'
import { useSocketJobStore } from '@stores/jobs'
import { useHead } from '@unhead/vue'
import Estimate from '@/pages/components/prod-categories/Estimate.vue'
import googleHome from '@images/pages/google-home.png'
import iphone11 from '@images/pages/iphone-11.png'

const store = useSocketStore()
const storeJobs = useSocketJobStore()

const { user } = storeToRefs(store)
const { job } = storeToRefs(storeJobs)

definePage({
  meta: {
    layout: 'default',
  },
})

useHead({
  title: 'Qwote Z | Production',
  meta: [
    {
      name: 'description',
      content: 'My page description',
    },
  ],
})

const checkoutSteps = [
  {
    title: 'Estimate',
    icon: 'teenyicons:cost-estimate-outline',
  },
  {
    title: 'Production',
    icon: 'emojione-monotone:movie-camera',
  },
  {
    title: 'Invoice',
    icon: 'fa6-solid:file-invoice-dollar',
  },
]

const checkoutData = ref({
  cartItems: [
    {
      id: 1,
      name: 'Google - Google Home - White',
      seller: 'Google',
      inStock: true,
      rating: 4,
      price: 299,
      discountPrice: 359,
      image: googleHome,
      quantity: 1,
      estimatedDelivery: '18th Nov 2021',
    },
    {
      id: 2,
      name: 'Apple iPhone 11 (64GB, Black)',
      seller: 'Apple',
      inStock: true,
      rating: 4,
      price: 899,
      discountPrice: 999,
      image: iphone11,
      quantity: 1,
      estimatedDelivery: '20th Nov 2021',
    },
  ],
  promoCode: '',
  orderAmount: 1198,
  deliveryAddress: 'home',
  deliverySpeed: 'free',
  deliveryCharges: 0,
  addresses: [
    {
      title: 'John Doe (Default)',
      desc: '4135 Parkway Street, Los Angeles, CA, 90017',
      subtitle: '1234567890',
      value: 'home',
    },
    {
      title: 'ACME Inc.',
      desc: '87 Hoffman Avenue, New York, NY, 10016',
      subtitle: '1234567890',
      value: 'office',
    },
  ],
})

const currentStep = ref(0)
const showBottomDrawer = ref(false)
const name = ref('')
const client = ref('')
const date = ref('')
const selectedRevision = ref('Estimate Revision 1')

const revisions = ref([
  'Estimate Revision 1',
  'Estimate Revision 2',
  'Estimate Revision 3',
  'Estimate Revision 4',
  'Estimate Revision 5',
])

const selectedCurrency = ref('ZAR')

const currencies = ref([
  'ZAR',
  'USD',
  'POUND',
  'EURO',
])

const estimateData = ref({
  groups: [],
  production: true,
})

const calculations = computed({
  get() {
    let commsItems = []
    let commsMarkupItems = []
    let totalsItems = []
    let subTotalsItems = []
    let markupItems = []
    let feeItems = []

    let commsMarkupFinal = 0
    let commsFinal = 0
    let totalsFinal = 0
    let subTotalsFinal = 0
    let markupFinal = 0
    let feeFinal = 0

    estimateData.value.groups.forEach(item => {
      item.items.forEach(i => {
        if (i.lineItemName === 'Agency Commission') {
          console.log(i.qty * i.rate)
          commsItems.push({
            comms: i.qty * i.time * i.rate,
          })
          commsMarkupItems.push({
            commsMarkup: i.markUp * 1,
          })
        }
        if (item.name === 'Fees') {
          feeItems.push({
            fee: i.qty * i.time * i.rate,
          })
          commsMarkupItems.push({
            commsMarkup: i.markUp * 1,
          })
        }
        markupItems.push({
          markup: i.markUp * 1,
        })
        subTotalsItems.push({
          total: i.qty * i.time * i.rate,
        })
        totalsItems.push({
          total: i.qty * i.time * i.rate,
        })
      })
    })

    commsItems.forEach(item => {
      commsFinal += item.comms
    })
    commsMarkupItems.forEach(item => {
      commsMarkupFinal += item.commsMarkup
    })
    markupItems.forEach(item => {
      markupFinal += item.markup
    })
    feeItems.forEach(item => {
      feeFinal += item.fee
    })
    subTotalsItems.forEach(item => {
      subTotalsFinal += item.total
    })
    totalsItems.forEach(item => {
      totalsFinal += item.total
    })

    console.log({ commsMarkupFinal })

    return {
      commsFinal,
      commsMarkupFinal,
      subTotalsItems,
      markupFinal,
      feeFinal,
      subTotalsFinal,
      totalsFinal,
    }
  },
  set(val) {
    val.forEach(item => {
      console.log({ item })
    })
  },
})

watch(calculations, val => {
  console.log('Calculations', { val })
})

const submitProductionEstimate = () => {
  console.log('Clicked Estimate Production')
}
</script>

<template>
  <VBtn
    color="primary"
    variant="text"
    @click="showBottomDrawer = !showBottomDrawer"
  >
    Quick Totals
  </VBtn>
  <Estimate :grouped-items="estimateData.groups" />
  <VBtn
    color="primary"
    variant="tonal"
    @click="submitProductionEstimate"
  >
    Submit Production Estimate
  </VBtn>

  <div>
    {{ job }}
  </div>
  <VBottomNavigation
    :active="showBottomDrawer"
    color="primary"
    height="auto"
  >
    <VRow>
      <VCol cols="12">
        <VCard>
          <AppCardActions action-collapsed>
            <VCardText>
              <VRow style="margin-bottom: -40px">
                <VCol
                  cols="6"
                  md="2"
                  lg="2"
                >
                  <p v-if="currentStep === 0">
                    Estimate Date:
                  </p>
                  <p v-else-if="currentStep === 1">
                    Production Date:
                  </p>
                  <p v-else-if="currentStep === 2">
                    Invoice Date:
                  </p>
                </VCol>
                <VCol
                  cols="6"
                  md="2"
                  lg="2"
                >
                  {{ date }}
                </VCol>
                <VCol
                  cols="6"
                  md="2"
                  lg="2"
                />
                <VCol
                  cols="6"
                  md="2"
                  lg="2"
                />
                <VCol
                  cols="6"
                  md="2"
                  lg="2"
                >
                  <p>Sub Total:</p>
                </VCol>
                <VCol
                  cols="6"
                  md="2"
                  lg="2"
                >
                  {{ calculations.subTotalsFinal }}
                </VCol>
              </VRow>
              <VRow style="margin-bottom: -40px">
                <VCol
                  cols="6"
                  md="2"
                  lg="2"
                >
                  <p>Fees Total:</p>
                </VCol>
                <VCol
                  cols="6"
                  md="2"
                  lg="2"
                >
                  {{ calculations.feeFinal }}
                </VCol>
                <VCol
                  cols="6"
                  md="2"
                  lg="2"
                />
                <VCol
                  cols="6"
                  md="2"
                  lg="2"
                />
                <VCol
                  cols="6"
                  md="2"
                  lg="2"
                >
                  <p>Total Taxable:</p>
                </VCol>
                <VCol
                  cols="6"
                  md="2"
                  lg="2"
                >
                  1
                </VCol>
              </VRow>
              <VRow style="margin-bottom: -40px">
                <VCol
                  cols="6"
                  md="2"
                  lg="2"
                >
                  <p>Production Total:</p>
                </VCol>
                <VCol
                  cols="6"
                  md="2"
                  lg="2"
                >
                  1
                </VCol>
                <VCol
                  cols="6"
                  md="2"
                  lg="2"
                >
                  <p>Payments Received:</p>
                </VCol>
                <VCol
                  cols="6"
                  md="2"
                  lg="2"
                >
                  1
                </VCol>
                <VCol
                  cols="6"
                  md="2"
                  lg="2"
                >
                  <p>Taxes Total:</p>
                </VCol>
                <VCol
                  cols="6"
                  md="2"
                  lg="2"
                >
                  1
                </VCol>
              </VRow>
              <VRow style="margin-bottom: -40px">
                <VCol
                  cols="6"
                  md="2"
                  lg="2"
                >
                  <p>Agent's Cut:</p>
                </VCol>
                <VCol
                  cols="6"
                  md="2"
                  lg="2"
                >
                  {{ calculations.commsFinal }}
                </VCol>
                <VCol
                  cols="6"
                  md="2"
                  lg="2"
                >
                  <p>Markup:</p>
                </VCol>
                <VCol
                  cols="6"
                  md="2"
                  lg="2"
                >
                  {{ calculations.commsMarkupFinal }}
                </VCol>
                <VCol
                  cols="6"
                  md="2"
                  lg="2"
                >
                  <p>Total ({{ selectedCurrency }}):</p>
                </VCol>
                <VCol
                  cols="6"
                  md="2"
                  lg="2"
                >
                  {{ calculations.totalsFinal }}
                </VCol>
              </VRow>
              <VRow style="margin-bottom: -40px">
                <VCol
                  cols="6"
                  md="2"
                  lg="2"
                >
                  <p>Markup Total:</p>
                </VCol>
                <VCol
                  cols="6"
                  md="2"
                  lg="2"
                >
                  {{ calculations.markupFinal }}
                </VCol>
                <VCol
                  cols="6"
                  md="2"
                  lg="2"
                >
                  <p>Estimated Profit:</p>
                </VCol>
                <VCol
                  cols="6"
                  md="2"
                  lg="2"
                >
                  1
                </VCol>
                <VCol
                  cols="6"
                  md="2"
                  lg="2"
                >
                  <p>Balance Due:</p>
                </VCol>
                <VCol
                  cols="6"
                  md="2"
                  lg="2"
                >
                  {{ calculations.totalsFinal }}
                </VCol>
              </VRow>
            </VCardText>
          </AppCardActions>
        </VCard>
      </VCol>
    </VRow>
  </VBottomNavigation>
</template>

<style scoped lang="scss">
.v-card-item {
  align-items: center;
  display: grid;
  flex: none;
  grid-template-areas: "prepend content append";
  grid-template-columns: max-content auto max-content;
  padding: 0px !important;
}
</style>
