<script setup>
import { useSocketRentalBookingStore } from '@stores/rentals/bookings'
import { useSocketRentalStore } from '@stores/rentals'
import { useSocketStore } from '@stores/auth'
import { socket } from '@socket/socket'
import { useHead } from '@unhead/vue'
import { toast } from 'vue3-toastify'
import 'vue3-toastify/dist/index.css'
import { useTheme } from 'vuetify'
import AppTextField from '@core/components/app-form-elements/AppTextField.vue'
import AppTextarea from '@core/components/app-form-elements/AppTextarea.vue'

const route = useRoute()
const bookingId = route.params.id

useHead({
  title: 'Qwotzee | Booking Details',
  meta: [
    {
      name: 'description',
      content: 'View and manage rental booking details',
    },
  ],
})

const storeBooking = useSocketRentalBookingStore()
const storeRental = useSocketRentalStore()
const storeAuth = useSocketStore()

const { booking } = storeToRefs(storeBooking)
const { rentals } = storeToRefs(storeRental)
const { user } = storeToRefs(storeAuth)

const router = useRouter()
const vuetifyTheme = useTheme()

// Socket event listeners for booking
socket.on('booking', data => {
  console.log({ data })
  if (data.status === 'success') {
    booking.value = data.data
    isLoading.value = false
  }
})

socket.on('bookingError', data => {
  console.error(data)
  toast(data.message, {
    autoClose: 5000,
    theme: vuetifyTheme.global.name.value,
    type: 'error',
  })
  isLoading.value = false
})

socket.on('updateBookingComplete', data => {
  console.log({ data })
  if (data.status === 'success') {
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })
    storeBooking.getBooking({ user: user.value._id, bookingId })
  }
})

socket.on('updateBookingError', data => {
  console.error(data)
  toast(data.message, {
    autoClose: 5000,
    theme: vuetifyTheme.global.name.value,
    type: 'error',
  })
})

socket.on('changeStatusComplete', data => {
  console.log({ data })
  if (data.status === 'success') {
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })
    statusDialog.value = false
    storeBooking.getBooking({ user: user.value._id, bookingId })
  }
})

socket.on('changeStatusError', data => {
  console.error(data)
  toast(data.message, {
    autoClose: 5000,
    theme: vuetifyTheme.global.name.value,
    type: 'error',
  })
})

socket.on('checkOutComplete', data => {
  console.log({ data })
  if (data.status === 'success') {
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })
    checkOutDialog.value = false
    storeBooking.getBooking({ user: user.value._id, bookingId })
  }
})

socket.on('checkOutError', data => {
  console.error(data)
  toast(data.message, {
    autoClose: 5000,
    theme: vuetifyTheme.global.name.value,
    type: 'error',
  })
})

socket.on('checkInComplete', data => {
  console.log({ data })
  if (data.status === 'success') {
    toast(data.message, {
      autoClose: 5000,
      theme: vuetifyTheme.global.name.value,
      type: 'success',
    })
    checkInDialog.value = false
    storeBooking.getBooking({ user: user.value._id, bookingId })
  }
})

socket.on('checkInError', data => {
  console.error(data)
  toast(data.message, {
    autoClose: 5000,
    theme: vuetifyTheme.global.name.value,
    type: 'error',
  })
})

// Form data
const selectedStatus = ref('')
const statusDialog = ref(false)
const checkOutDialog = ref(false)
const checkInDialog = ref(false)
const isLoading = ref(true)

// Check-out form data
const checkOutStaff = ref('')
const checkOutNotes = ref('')
const depositAmount = ref(0)
const depositPaid = ref(false)
const customerName = ref('')
const customerEmail = ref('')
const customerPhone = ref('')

// Check-in form data
const checkInStaff = ref('')
const checkInNotes = ref('')
const lateFee = ref(0)
const damageFee = ref(0)
const paymentStatus = ref('unpaid')

// Status options
const statusOptions = [
  { title: 'Pending', value: 'pending' },
  { title: 'Confirmed', value: 'confirmed' },
  { title: 'Checked Out', value: 'checked-out' },
  { title: 'Checked In', value: 'checked-in' },
  { title: 'Cancelled', value: 'cancelled' },
  { title: 'Completed', value: 'completed' },
]

const paymentStatusOptions = [
  { title: 'Unpaid', value: 'unpaid' },
  { title: 'Deposit Paid', value: 'deposit-paid' },
  { title: 'Partially Paid', value: 'partially-paid' },
  { title: 'Fully Paid', value: 'fully-paid' },
  { title: 'Refunded', value: 'refunded' },
]

// Fetch data on component mount
onMounted(() => {
  storeBooking.getBooking({ user: user.value._id, bookingId })
  storeRental.getRentals({ user: user.value._id })
})

// Computed properties
const canCheckOut = computed(() => {
  if (!booking.value) return false
  
  return ['pending', 'confirmed'].includes(booking.value.status)
})

const canCheckIn = computed(() => {
  if (!booking.value) return false
  
  return booking.value.status === 'checked-out'
})

const bookingItems = computed(() => {
  if (!booking.value || !booking.value.items || !rentals.value) return []
  
  return booking.value.items.map(itemId => {
    const item = rentals.value.find(r => r._id === itemId)
    
    return item || { title: 'Unknown Item', _id: itemId }
  })
})

// Methods
const openStatusDialog = () => {
  selectedStatus.value = booking.value.status
  statusDialog.value = true
}

const openCheckOutDialog = () => {
  checkOutStaff.value = user.value._id
  checkOutNotes.value = ''
  depositAmount.value = 0
  depositPaid.value = false
  customerName.value = ''
  customerEmail.value = ''
  customerPhone.value = ''
  checkOutDialog.value = true
}

const openCheckInDialog = () => {
  checkInStaff.value = user.value._id
  checkInNotes.value = ''
  lateFee.value = 0
  damageFee.value = 0
  paymentStatus.value = booking.value.paymentStatus || 'unpaid'
  checkInDialog.value = true
}

const confirmStatusChange = () => {
  storeBooking.changeBookingStatus({
    user: user.value._id,
    bookingId,
    status: selectedStatus.value,
  })
}

const confirmCheckOut = () => {
  const customer = {
    name: customerName.value,
    email: customerEmail.value,
    phone: customerPhone.value,
  }
  
  storeBooking.checkOutBooking({
    user: user.value._id,
    bookingId,
    checkOutStaff: checkOutStaff.value,
    checkOutNotes: checkOutNotes.value,
    depositAmount: parseFloat(depositAmount.value) || 0,
    depositPaid: depositPaid.value,
    customer,
  })
}

const confirmCheckIn = () => {
  storeBooking.checkInBooking({
    user: user.value._id,
    bookingId,
    checkInStaff: checkInStaff.value,
    checkInNotes: checkInNotes.value,
    lateFee: parseFloat(lateFee.value) || 0,
    damageFee: parseFloat(damageFee.value) || 0,
    paymentStatus: paymentStatus.value,
  })
}

const navigateToBookings = () => {
  router.push({ name: 'rentals-bookings' })
}

const formatDate = dateString => {
  if (!dateString) return ''
  
  const date = new Date(dateString)
  
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  })
}

const formatDateTime = dateString => {
  if (!dateString) return ''
  
  const date = new Date(dateString)
  
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  })
}

const getStatusColor = status => {
  switch (status) {
  case 'pending':
    return 'warning'
  case 'confirmed':
    return 'info'
  case 'checked-out':
    return 'error'
  case 'checked-in':
    return 'success'
  case 'cancelled':
    return 'grey'
  case 'completed':
    return 'success'
  default:
    return 'grey'
  }
}

const formatCurrency = value => {
  if (!value && value !== 0) return '$0.00'
  
  return `$${parseFloat(value).toFixed(2)}`
}
</script>

<template>
  <VBreadcrumbs class="px-0 pb-5 pt-0 flex-wrap">
    <VBreadcrumbsItem
      title="Rental Management"
      @click="() => router.push({ name: 'rentals' })"
    />
    <VBreadcrumbsDivider />
    <VBreadcrumbsItem
      title="Bookings"
      @click="navigateToBookings"
    />
    <VBreadcrumbsDivider />
    <VBreadcrumbsItem
      title="Booking Details"
      disabled
    />
  </VBreadcrumbs>

  <div v-if="!isLoading && booking">
    <VRow>
      <!-- Booking Header -->
      <VCol cols="12">
        <VCard>
          <VCardText>
            <div class="d-flex justify-space-between align-center flex-wrap">
              <div>
                <h2 class="text-h4 mb-2">
                  {{ booking.title }}
                </h2>
                <div class="d-flex align-center gap-4 mb-2">
                  <VChip
                    :color="getStatusColor(booking.status)"
                    size="small"
                  >
                    {{ booking.status }}
                  </VChip>
                  <span>{{ formatDate(booking.startTime) }} - {{ formatDate(booking.endTime) }}</span>
                </div>
                <div v-if="booking.location">
                  <VIcon
                    icon="tabler-map-pin"
                    size="small"
                    class="mr-1"
                  />
                  {{ booking.location }}
                </div>
              </div>
              <div class="d-flex gap-2">
                <VBtn
                  v-if="canCheckOut"
                  color="warning"
                  prepend-icon="tabler-logout"
                  @click="openCheckOutDialog"
                >
                  Check Out
                </VBtn>
                <VBtn
                  v-if="canCheckIn"
                  color="success"
                  prepend-icon="tabler-login"
                  @click="openCheckInDialog"
                >
                  Check In
                </VBtn>
                <VBtn
                  color="primary"
                  prepend-icon="tabler-exchange"
                  @click="openStatusDialog"
                >
                  Change Status
                </VBtn>
              </div>
            </div>
          </VCardText>
        </VCard>
      </VCol>

      <!-- Booking Details -->
      <VCol
        cols="12"
        md="6"
      >
        <VCard>
          <VCardTitle>Booking Details</VCardTitle>
          <VCardText>
            <VList>
              <VListItem>
                <template #prepend>
                  <VIcon
                    icon="tabler-calendar"
                    class="mr-2"
                  />
                </template>
                <VListItemTitle>Date Range</VListItemTitle>
                <VListItemSubtitle>{{ formatDate(booking.startTime) }} - {{ formatDate(booking.endTime) }}</VListItemSubtitle>
              </VListItem>

              <VListItem>
                <template #prepend>
                  <VIcon
                    icon="tabler-clock"
                    class="mr-2"
                  />
                </template>
                <VListItemTitle>Time</VListItemTitle>
                <VListItemSubtitle>
                  {{ new Date(booking.startTime).toLocaleTimeString() }} - {{ new Date(booking.endTime).toLocaleTimeString() }}
                  <VChip
                    v-if="booking.allDay"
                    size="x-small"
                    color="info"
                    class="ml-2"
                  >
                    All Day
                  </VChip>
                </VListItemSubtitle>
              </VListItem>

              <VListItem v-if="booking.location">
                <template #prepend>
                  <VIcon
                    icon="tabler-map-pin"
                    class="mr-2"
                  />
                </template>
                <VListItemTitle>Location</VListItemTitle>
                <VListItemSubtitle>{{ booking.location }}</VListItemSubtitle>
              </VListItem>

              <VListItem v-if="booking.notes">
                <template #prepend>
                  <VIcon
                    icon="tabler-notes"
                    class="mr-2"
                  />
                </template>
                <VListItemTitle>Notes</VListItemTitle>
                <VListItemSubtitle>{{ booking.notes }}</VListItemSubtitle>
              </VListItem>

              <VListItem>
                <template #prepend>
                  <VIcon
                    icon="tabler-flag"
                    class="mr-2"
                  />
                </template>
                <VListItemTitle>Priority</VListItemTitle>
                <VListItemSubtitle>
                  <VChip
                    :color="booking.priority === 'high' ? 'error' : booking.priority === 'medium' ? 'warning' : 'success'"
                    size="x-small"
                  >
                    {{ booking.priority }}
                  </VChip>
                </VListItemSubtitle>
              </VListItem>

              <VListItem>
                <template #prepend>
                  <VIcon
                    icon="tabler-calendar-stats"
                    class="mr-2"
                  />
                </template>
                <VListItemTitle>Created</VListItemTitle>
                <VListItemSubtitle>{{ formatDateTime(booking.createdAt) }}</VListItemSubtitle>
              </VListItem>

              <VListItem>
                <template #prepend>
                  <VIcon
                    icon="tabler-refresh"
                    class="mr-2"
                  />
                </template>
                <VListItemTitle>Last Updated</VListItemTitle>
                <VListItemSubtitle>{{ formatDateTime(booking.lastUpdated) }}</VListItemSubtitle>
              </VListItem>
            </VList>
          </VCardText>
        </VCard>
      </VCol>

      <!-- Financial Information -->
      <VCol
        cols="12"
        md="6"
      >
        <VCard>
          <VCardTitle>Financial Information</VCardTitle>
          <VCardText>
            <VList>
              <VListItem>
                <template #prepend>
                  <VIcon
                    icon="tabler-cash"
                    class="mr-2"
                  />
                </template>
                <VListItemTitle>Deposit Amount</VListItemTitle>
                <VListItemSubtitle>{{ formatCurrency(booking.depositAmount) }}</VListItemSubtitle>
              </VListItem>

              <VListItem>
                <template #prepend>
                  <VIcon
                    icon="tabler-receipt"
                    class="mr-2"
                  />
                </template>
                <VListItemTitle>Rental Fee</VListItemTitle>
                <VListItemSubtitle>{{ formatCurrency(booking.rentalFee) }}</VListItemSubtitle>
              </VListItem>

              <VListItem v-if="booking.lateFee">
                <template #prepend>
                  <VIcon
                    icon="tabler-clock"
                    class="mr-2"
                  />
                </template>
                <VListItemTitle>Late Fee</VListItemTitle>
                <VListItemSubtitle>{{ formatCurrency(booking.lateFee) }}</VListItemSubtitle>
              </VListItem>

              <VListItem v-if="booking.damageFee">
                <template #prepend>
                  <VIcon
                    icon="tabler-alert-triangle"
                    class="mr-2"
                  />
                </template>
                <VListItemTitle>Damage Fee</VListItemTitle>
                <VListItemSubtitle>{{ formatCurrency(booking.damageFee) }}</VListItemSubtitle>
              </VListItem>

              <VListItem>
                <template #prepend>
                  <VIcon
                    icon="tabler-sum"
                    class="mr-2"
                  />
                </template>
                <VListItemTitle>Total Fee</VListItemTitle>
                <VListItemSubtitle>{{ formatCurrency(booking.totalFee) }}</VListItemSubtitle>
              </VListItem>

              <VListItem>
                <template #prepend>
                  <VIcon
                    icon="tabler-credit-card"
                    class="mr-2"
                  />
                </template>
                <VListItemTitle>Payment Status</VListItemTitle>
                <VListItemSubtitle>
                  <VChip
                    :color="booking.paymentStatus === 'fully-paid' ? 'success' : booking.paymentStatus === 'unpaid' ? 'error' : 'warning'"
                    size="x-small"
                  >
                    {{ booking.paymentStatus }}
                  </VChip>
                </VListItemSubtitle>
              </VListItem>

              <VListItem v-if="booking.depositPaid">
                <template #prepend>
                  <VIcon
                    icon="tabler-check"
                    class="mr-2"
                  />
                </template>
                <VListItemTitle>Deposit Status</VListItemTitle>
                <VListItemSubtitle>
                  <VChip
                    color="success"
                    size="x-small"
                  >
                    Paid
                  </VChip>
                </VListItemSubtitle>
              </VListItem>
            </VList>
          </VCardText>
        </VCard>
      </VCol>

      <!-- Rental Items -->
      <VCol cols="12">
        <VCard>
          <VCardTitle>Rental Items</VCardTitle>
          <VCardText>
            <VTable>
              <thead>
                <tr>
                  <th>ITEM</th>
                  <th>SERIAL NUMBER</th>
                  <th>CONDITION</th>
                  <th>DAILY RATE</th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="item in bookingItems"
                  :key="item._id"
                >
                  <td>{{ item.title }}</td>
                  <td>{{ item.serialNumber || 'N/A' }}</td>
                  <td>{{ item.condition || 'N/A' }}</td>
                  <td>{{ formatCurrency(item.dailyRate) }}</td>
                </tr>
              </tbody>
            </VTable>
          </VCardText>
        </VCard>
      </VCol>

      <!-- Check-out Information -->
      <VCol
        v-if="booking.status === 'checked-out' || booking.status === 'checked-in'"
        cols="12"
        md="6"
      >
        <VCard>
          <VCardTitle>Check-out Information</VCardTitle>
          <VCardText>
            <VList>
              <VListItem>
                <template #prepend>
                  <VIcon
                    icon="tabler-calendar"
                    class="mr-2"
                  />
                </template>
                <VListItemTitle>Check-out Time</VListItemTitle>
                <VListItemSubtitle>{{ formatDateTime(booking.checkOutTime) }}</VListItemSubtitle>
              </VListItem>

              <VListItem v-if="booking.checkOutNotes">
                <template #prepend>
                  <VIcon
                    icon="tabler-notes"
                    class="mr-2"
                  />
                </template>
                <VListItemTitle>Check-out Notes</VListItemTitle>
                <VListItemSubtitle>{{ booking.checkOutNotes }}</VListItemSubtitle>
              </VListItem>

              <VListItem v-if="booking.customer && booking.customer.name">
                <template #prepend>
                  <VIcon
                    icon="tabler-user"
                    class="mr-2"
                  />
                </template>
                <VListItemTitle>Customer</VListItemTitle>
                <VListItemSubtitle>{{ booking.customer.name }}</VListItemSubtitle>
              </VListItem>

              <VListItem v-if="booking.customer && booking.customer.email">
                <template #prepend>
                  <VIcon
                    icon="tabler-mail"
                    class="mr-2"
                  />
                </template>
                <VListItemTitle>Email</VListItemTitle>
                <VListItemSubtitle>{{ booking.customer.email }}</VListItemSubtitle>
              </VListItem>

              <VListItem v-if="booking.customer && booking.customer.phone">
                <template #prepend>
                  <VIcon
                    icon="tabler-phone"
                    class="mr-2"
                  />
                </template>
                <VListItemTitle>Phone</VListItemTitle>
                <VListItemSubtitle>{{ booking.customer.phone }}</VListItemSubtitle>
              </VListItem>
            </VList>
          </VCardText>
        </VCard>
      </VCol>

      <!-- Check-in Information -->
      <VCol
        v-if="booking.status === 'checked-in'"
        cols="12"
        md="6"
      >
        <VCard>
          <VCardTitle>Check-in Information</VCardTitle>
          <VCardText>
            <VList>
              <VListItem>
                <template #prepend>
                  <VIcon
                    icon="tabler-calendar"
                    class="mr-2"
                  />
                </template>
                <VListItemTitle>Check-in Time</VListItemTitle>
                <VListItemSubtitle>{{ formatDateTime(booking.checkInTime) }}</VListItemSubtitle>
              </VListItem>

              <VListItem v-if="booking.checkInNotes">
                <template #prepend>
                  <VIcon
                    icon="tabler-notes"
                    class="mr-2"
                  />
                </template>
                <VListItemTitle>Check-in Notes</VListItemTitle>
                <VListItemSubtitle>{{ booking.checkInNotes }}</VListItemSubtitle>
              </VListItem>

              <VListItem v-if="booking.lateFee > 0">
                <template #prepend>
                  <VIcon
                    icon="tabler-clock"
                    class="mr-2"
                  />
                </template>
                <VListItemTitle>Late Fee</VListItemTitle>
                <VListItemSubtitle>{{ formatCurrency(booking.lateFee) }}</VListItemSubtitle>
              </VListItem>

              <VListItem v-if="booking.damageFee > 0">
                <template #prepend>
                  <VIcon
                    icon="tabler-alert-triangle"
                    class="mr-2"
                  />
                </template>
                <VListItemTitle>Damage Fee</VListItemTitle>
                <VListItemSubtitle>{{ formatCurrency(booking.damageFee) }}</VListItemSubtitle>
              </VListItem>
            </VList>
          </VCardText>
        </VCard>
      </VCol>
    </VRow>
  </div>

  <VProgressCircular
    v-else-if="isLoading"
    indeterminate
    class="ma-4"
  />

  <VCard v-else>
    <VCardText>
      <div class="d-flex justify-center align-center pa-4">
        <p>Booking not found</p>
      </div>
    </VCardText>
  </VCard>

  <!-- Change Status Dialog -->
  <VDialog
    v-model="statusDialog"
    max-width="500px"
  >
    <VCard>
      <VCardTitle>Change Booking Status</VCardTitle>
      <VCardText>
        <VSelect
          v-model="selectedStatus"
          :items="statusOptions"
          label="Status"
        />
      </VCardText>
      <VCardActions>
        <VSpacer />
        <VBtn
          color="secondary"
          variant="tonal"
          @click="statusDialog = false"
        >
          Cancel
        </VBtn>
        <VBtn
          color="primary"
          @click="confirmStatusChange"
        >
          Update Status
        </VBtn>
      </VCardActions>
    </VCard>
  </VDialog>

  <!-- Check Out Dialog -->
  <VDialog
    v-model="checkOutDialog"
    max-width="800px"
  >
    <VCard>
      <VCardTitle>Check Out Items</VCardTitle>
      <VCardText>
        <VForm @submit.prevent="confirmCheckOut">
          <VRow>
            <VCol
              cols="12"
              md="6"
            >
              <AppTextField
                v-model="depositAmount"
                label="Deposit Amount"
                type="number"
                min="0"
                step="0.01"
                prefix="$"
              />
            </VCol>

            <VCol
              cols="12"
              md="6"
            >
              <VCheckbox
                v-model="depositPaid"
                label="Deposit Paid"
              />
            </VCol>

            <VCol
              cols="12"
              md="6"
            >
              <AppTextField
                v-model="customerName"
                label="Customer Name"
                placeholder="Enter customer name"
              />
            </VCol>

            <VCol
              cols="12"
              md="6"
            >
              <AppTextField
                v-model="customerEmail"
                label="Customer Email"
                placeholder="Enter customer email"
              />
            </VCol>

            <VCol
              cols="12"
              md="6"
            >
              <AppTextField
                v-model="customerPhone"
                label="Customer Phone"
                placeholder="Enter customer phone"
              />
            </VCol>

            <VCol cols="12">
              <AppTextarea
                v-model="checkOutNotes"
                label="Check-out Notes"
                placeholder="Enter check-out notes"
              />
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
      <VCardActions>
        <VSpacer />
        <VBtn
          color="secondary"
          variant="tonal"
          @click="checkOutDialog = false"
        >
          Cancel
        </VBtn>
        <VBtn
          color="warning"
          @click="confirmCheckOut"
        >
          Check Out
        </VBtn>
      </VCardActions>
    </VCard>
  </VDialog>

  <!-- Check In Dialog -->
  <VDialog
    v-model="checkInDialog"
    max-width="800px"
  >
    <VCard>
      <VCardTitle>Check In Items</VCardTitle>
      <VCardText>
        <VForm @submit.prevent="confirmCheckIn">
          <VRow>
            <VCol
              cols="12"
              md="6"
            >
              <AppTextField
                v-model="lateFee"
                label="Late Fee"
                type="number"
                min="0"
                step="0.01"
                prefix="$"
              />
            </VCol>

            <VCol
              cols="12"
              md="6"
            >
              <AppTextField
                v-model="damageFee"
                label="Damage Fee"
                type="number"
                min="0"
                step="0.01"
                prefix="$"
              />
            </VCol>

            <VCol
              cols="12"
              md="6"
            >
              <VSelect
                v-model="paymentStatus"
                :items="paymentStatusOptions"
                label="Payment Status"
              />
            </VCol>

            <VCol cols="12">
              <AppTextarea
                v-model="checkInNotes"
                label="Check-in Notes"
                placeholder="Enter check-in notes"
              />
            </VCol>
          </VRow>
        </VForm>
      </VCardText>
      <VCardActions>
        <VSpacer />
        <VBtn
          color="secondary"
          variant="tonal"
          @click="checkInDialog = false"
        >
          Cancel
        </VBtn>
        <VBtn
          color="success"
          @click="confirmCheckIn"
        >
          Check In
        </VBtn>
      </VCardActions>
    </VCard>
  </VDialog>
</template>

<style scoped lang="scss">
//
</style>
